# 魂斗罗 Web版

一款基于HTML5 Canvas和JavaScript开发的经典横版射击游戏，重现了经典魂斗罗的游戏体验。

## 🎮 游戏特色

- **经典玩法**: 横版射击，消灭敌人，收集道具
- **多种武器**: 普通枪、机关枪、散弹枪、激光枪、火箭筒等
- **智能敌人**: 不同类型的敌人具有独特的AI行为模式
- **粒子特效**: 爆炸、火花、烟雾等丰富的视觉效果
- **关卡系统**: 多个关卡，难度递增
- **道具系统**: 生命、武器、分数等多种道具
- **响应式设计**: 支持不同屏幕尺寸

## 🕹️ 操作方式

### 键盘控制
- **WASD** 或 **方向键**: 移动角色
- **空格键**: 射击
- **P键**: 暂停/继续游戏
- **R键**: 重新开始
- **Q键**: 切换到上一个武器
- **E键**: 切换到下一个武器
- **X键**: 冲刺（有冷却时间）
- **F1**: 显示/隐藏调试信息
- **F11**: 全屏模式

### 鼠标控制
- **鼠标左键**: 射击
- **鼠标移动**: 瞄准（预留功能）

### 手柄支持
- 支持标准游戏手柄
- 左摇杆控制移动
- A键或RT键射击

## 🎯 游戏目标

1. **消灭敌人**: 击败所有出现的敌人
2. **收集道具**: 获取武器升级和生命补给
3. **获得高分**: 尽可能获得更高的分数
4. **通关**: 完成所有关卡的挑战

## 🛠️ 技术特性

### 游戏引擎
- **渲染系统**: HTML5 Canvas 2D渲染
- **物理系统**: 自定义物理引擎，支持重力、碰撞检测
- **输入系统**: 键盘、鼠标、手柄多输入支持
- **音频系统**: Web Audio API（预留）

### 核心系统
- **对象池**: 优化内存使用，减少垃圾回收
- **碰撞检测**: 高效的矩形和圆形碰撞检测
- **粒子系统**: 丰富的视觉特效
- **状态机**: 游戏状态和AI状态管理
- **事件系统**: 解耦的事件通信机制

### 性能优化
- **帧率控制**: 60FPS目标帧率
- **性能监控**: 实时FPS和性能指标
- **内存管理**: 对象池和资源回收
- **渲染优化**: 视锥剔除和批量渲染

## 📁 项目结构

```
魂斗罗2/
├── index.html          # 主页面
├── css/
│   └── style.css       # 样式文件
├── js/
│   ├── utils.js        # 工具函数
│   ├── input.js        # 输入处理
│   ├── collision.js    # 碰撞检测
│   ├── particle.js     # 粒子系统
│   ├── bullet.js       # 子弹系统
│   ├── weapon.js       # 武器系统
│   ├── player.js       # 玩家角色
│   ├── enemy.js        # 敌人系统
│   ├── powerup.js      # 道具系统
│   ├── level.js        # 关卡系统
│   ├── game.js         # 主游戏逻辑
│   └── main.js         # 入口文件
└── README.md           # 说明文档
```

## 🎨 游戏元素

### 武器类型
- **普通枪**: 平衡的射速和伤害
- **机关枪**: 高射速，低伤害
- **散弹枪**: 扇形射击，近距离威力大
- **激光枪**: 高伤害，可穿透敌人
- **火箭筒**: 爆炸伤害，弹药有限
- **火焰枪**: 火焰攻击，持续伤害

### 敌人类型
- **士兵**: 基础敌人，单发射击
- **重装兵**: 高血量，三连发攻击
- **快速兵**: 移动迅速，攻击频繁
- **狙击手**: 远程精确射击
- **Boss**: 多种攻击模式，高血量

### 道具类型
- **生命包**: 恢复生命值
- **生命数**: 增加生命数量
- **武器道具**: 获得新武器
- **分数道具**: 增加得分
- **速度提升**: 临时增加移动速度
- **无敌状态**: 短时间无敌
- **二段跳**: 获得二段跳能力

## 🚀 运行游戏

1. **直接运行**: 在浏览器中打开 `index.html` 文件
2. **本地服务器**: 使用HTTP服务器运行（推荐）
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx http-server
   
   # 使用Live Server (VS Code扩展)
   ```
3. **在线部署**: 可部署到任何静态网站托管服务

## 🎮 游戏截图

游戏包含以下界面：
- 开始菜单
- 游戏主界面
- 暂停菜单
- 游戏结束界面
- 游戏说明界面

## 🔧 自定义和扩展

### 添加新武器
1. 在 `weapon.js` 中的 `setupWeaponStats()` 方法添加新武器类型
2. 设置武器属性（射速、伤害、子弹类型等）
3. 在道具系统中添加对应的武器道具

### 添加新敌人
1. 在 `enemy.js` 中的 `setupEnemyStats()` 方法添加新敌人类型
2. 实现敌人的AI行为和攻击模式
3. 在关卡系统中配置敌人生成

### 添加新关卡
1. 在 `level.js` 中修改关卡生成逻辑
2. 设计新的地图布局和敌人配置
3. 添加特殊的环境效果

## 🐛 已知问题

- 音频系统尚未完全实现
- 部分浏览器可能存在性能差异
- 移动端触控支持有限

## 📝 更新日志

### v1.0.0
- 完整的游戏核心功能
- 多种武器和敌人类型
- 粒子特效系统
- 关卡和道具系统
- 响应式界面设计

## 🤝 贡献

欢迎提交Issue和Pull Request来改进游戏！

## 📄 许可证

本项目仅供学习和娱乐使用。

---

**享受游戏吧！** 🎮✨
