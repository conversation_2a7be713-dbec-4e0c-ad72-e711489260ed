// 道具系统

class PowerUp {
    constructor(x, y, type = 'health') {
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        this.type = type;
        this.active = true;
        this.collected = false;
        
        // 动画
        this.animationTime = 0;
        this.bobOffset = 0;
        this.rotationSpeed = 2;
        this.bobSpeed = 3;
        this.bobRange = 5;
        
        // 生命周期
        this.lifeTime = 15; // 15秒后消失
        this.lifeTimer = 0;
        this.blinkTime = 3; // 最后3秒闪烁
        
        // 碰撞
        this.collisionType = 'rect';
        
        // 磁性吸引
        this.magnetRange = 50;
        this.magnetSpeed = 200;
        this.isMagnetic = false;
        
        this.setupPowerUpStats();
    }

    setupPowerUpStats() {
        switch (this.type) {
            case 'health':
                this.width = 16;
                this.height = 16;
                this.color = '#ff0000';
                this.effect = () => {
                    player.heal(1);
                    return '生命 +1';
                };
                break;
                
            case 'life':
                this.width = 20;
                this.height = 20;
                this.color = '#ff00ff';
                this.effect = () => {
                    player.addLife();
                    return '生命数 +1';
                };
                break;
                
            case 'weapon_machine':
                this.width = 18;
                this.height = 18;
                this.color = '#ff8800';
                this.effect = () => {
                    player.weaponManager.addWeapon('machine');
                    player.weaponManager.switchWeapon('machine');
                    return '获得机关枪';
                };
                break;
                
            case 'weapon_spread':
                this.width = 18;
                this.height = 18;
                this.color = '#00ff00';
                this.effect = () => {
                    player.weaponManager.addWeapon('spread');
                    player.weaponManager.switchWeapon('spread');
                    return '获得散弹枪';
                };
                break;
                
            case 'weapon_laser':
                this.width = 18;
                this.height = 18;
                this.color = '#ff0088';
                this.effect = () => {
                    player.weaponManager.addWeapon('laser');
                    player.weaponManager.switchWeapon('laser');
                    return '获得激光枪';
                };
                break;
                
            case 'weapon_rocket':
                this.width = 18;
                this.height = 18;
                this.color = '#ff4400';
                this.effect = () => {
                    player.weaponManager.addWeapon('rocket');
                    player.weaponManager.switchWeapon('rocket');
                    return '获得火箭筒';
                };
                break;
                
            case 'score':
                this.width = 14;
                this.height = 14;
                this.color = '#ffff00';
                this.effect = () => {
                    const bonus = 500;
                    player.addScore(bonus);
                    return `得分 +${bonus}`;
                };
                break;
                
            case 'speed':
                this.width = 16;
                this.height = 16;
                this.color = '#00ffff';
                this.lifeTime = 10; // 临时效果
                this.effect = () => {
                    // 临时增加移动速度
                    const originalSpeed = player.speed;
                    player.speed *= 1.5;
                    setTimeout(() => {
                        player.speed = originalSpeed;
                    }, 10000);
                    return '移动速度提升';
                };
                break;
                
            case 'invincible':
                this.width = 18;
                this.height = 18;
                this.color = '#ffffff';
                this.effect = () => {
                    player.invulnerable = true;
                    player.invulnerabilityTimer = 5;
                    return '无敌状态';
                };
                break;
                
            case 'double_jump':
                this.width = 16;
                this.height = 16;
                this.color = '#8888ff';
                this.effect = () => {
                    player.canDoubleJump = true;
                    return '获得二段跳';
                };
                break;
        }
    }

    update(deltaTime) {
        if (!this.active) return;

        this.animationTime += deltaTime;
        this.lifeTimer += deltaTime;
        
        // 上下浮动动画
        this.bobOffset = Math.sin(this.animationTime * this.bobSpeed) * this.bobRange;
        
        // 检查生命周期
        if (this.lifeTimer >= this.lifeTime) {
            this.active = false;
            return;
        }
        
        // 磁性吸引
        this.updateMagnetism(deltaTime);
        
        // 检查与玩家的碰撞
        this.checkPlayerCollision();
    }

    updateMagnetism(deltaTime) {
        const distanceToPlayer = MathUtils.distance(this.x, this.y, player.x, player.y);
        
        if (distanceToPlayer <= this.magnetRange) {
            this.isMagnetic = true;
            
            // 向玩家移动
            const angle = MathUtils.angle(this.x, this.y, player.x, player.y);
            const moveSpeed = this.magnetSpeed * deltaTime;
            
            this.x += Math.cos(angle) * moveSpeed;
            this.y += Math.sin(angle) * moveSpeed;
        }
    }

    checkPlayerCollision() {
        const playerBounds = player.getBounds();
        const powerupBounds = this.getBounds();
        
        if (collisionSystem.rectToRect(
            { x: player.x, y: player.y, width: player.width, height: player.height },
            { x: this.x, y: this.y, width: this.width, height: this.height }
        )) {
            this.collect();
        }
    }

    collect() {
        if (this.collected) return;
        
        this.collected = true;
        this.active = false;
        
        // 执行效果
        const message = this.effect();
        
        // 创建收集特效
        particleSystem.createSparks(this.x, this.y, 12, {
            colors: [this.color, '#ffffff', '#ffff00'],
            minSpeed: 80,
            maxSpeed: 120,
            life: 0.8,
            gravity: -50
        });
        
        // 显示消息（可以后续添加UI系统来显示）
        console.log(message);
        
        return message;
    }

    render(ctx) {
        if (!this.active) return;

        ctx.save();
        
        // 闪烁效果（生命周期快结束时）
        const timeLeft = this.lifeTime - this.lifeTimer;
        if (timeLeft <= this.blinkTime) {
            const blinkRate = 8;
            if (Math.floor(this.lifeTimer * blinkRate) % 2 === 0) {
                ctx.globalAlpha = 0.3;
            }
        }
        
        // 磁性吸引时的发光效果
        if (this.isMagnetic) {
            ctx.shadowColor = this.color;
            ctx.shadowBlur = 10;
        }
        
        ctx.translate(this.x, this.y + this.bobOffset);
        ctx.rotate(this.animationTime * this.rotationSpeed);
        
        // 渲染道具
        this.renderSprite(ctx);
        
        ctx.restore();
    }

    renderSprite(ctx) {
        switch (this.type) {
            case 'health':
                // 十字形血包
                ctx.fillStyle = this.color;
                ctx.fillRect(-2, -8, 4, 16);
                ctx.fillRect(-8, -2, 16, 4);
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 1;
                ctx.strokeRect(-2, -8, 4, 16);
                ctx.strokeRect(-8, -2, 16, 4);
                break;
                
            case 'life':
                // 心形
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(-4, -2, 4, 0, Math.PI * 2);
                ctx.arc(4, -2, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.beginPath();
                ctx.moveTo(-8, 2);
                ctx.lineTo(0, 10);
                ctx.lineTo(8, 2);
                ctx.closePath();
                ctx.fill();
                break;
                
            case 'score':
                // 星形
                ctx.fillStyle = this.color;
                ctx.beginPath();
                for (let i = 0; i < 5; i++) {
                    const angle = (i * Math.PI * 2) / 5 - Math.PI / 2;
                    const x = Math.cos(angle) * 8;
                    const y = Math.sin(angle) * 8;
                    if (i === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                    
                    const innerAngle = ((i + 0.5) * Math.PI * 2) / 5 - Math.PI / 2;
                    const innerX = Math.cos(innerAngle) * 4;
                    const innerY = Math.sin(innerAngle) * 4;
                    ctx.lineTo(innerX, innerY);
                }
                ctx.closePath();
                ctx.fill();
                break;
                
            case 'speed':
                // 闪电形
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.moveTo(-2, -8);
                ctx.lineTo(2, -2);
                ctx.lineTo(-1, -2);
                ctx.lineTo(3, 8);
                ctx.lineTo(-1, 2);
                ctx.lineTo(2, 2);
                ctx.closePath();
                ctx.fill();
                break;
                
            case 'invincible':
                // 盾牌形
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.moveTo(0, -8);
                ctx.lineTo(6, -4);
                ctx.lineTo(6, 4);
                ctx.lineTo(0, 8);
                ctx.lineTo(-6, 4);
                ctx.lineTo(-6, -4);
                ctx.closePath();
                ctx.fill();
                break;
                
            default:
                // 默认方形
                ctx.fillStyle = this.color;
                ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 2;
                ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);
        }
    }

    getBounds() {
        return {
            left: this.x - this.width/2,
            right: this.x + this.width/2,
            top: this.y - this.height/2,
            bottom: this.y + this.height/2
        };
    }
}

class PowerUpManager {
    constructor() {
        this.powerups = [];
        this.spawnTimer = 0;
        this.spawnInterval = 8; // 每8秒可能生成一个道具
        this.spawnChance = 0.3; // 30%概率生成
    }

    update(deltaTime) {
        // 更新所有道具
        for (let i = this.powerups.length - 1; i >= 0; i--) {
            const powerup = this.powerups[i];
            powerup.update(deltaTime);
            
            // 移除非活跃道具
            if (!powerup.active) {
                this.powerups.splice(i, 1);
            }
        }
        
        // 随机生成道具
        this.spawnTimer += deltaTime;
        if (this.spawnTimer >= this.spawnInterval) {
            if (Math.random() < this.spawnChance) {
                this.spawnRandomPowerUp();
            }
            this.spawnTimer = 0;
        }
    }

    spawnRandomPowerUp() {
        const types = [
            'health', 'score', 'speed', 'weapon_machine', 
            'weapon_spread', 'weapon_laser', 'invincible'
        ];
        
        // 根据游戏进度调整道具类型权重
        let weightedTypes = [...types];
        if (player.lives < 2) {
            weightedTypes.push('health', 'life'); // 生命值低时更容易出现血包
        }
        
        const type = ArrayUtils.randomChoice(weightedTypes);
        const x = MathUtils.randomFloat(50, 750);
        const y = MathUtils.randomFloat(100, 500);
        
        this.createPowerUp(x, y, type);
    }

    createPowerUp(x, y, type) {
        const powerup = new PowerUp(x, y, type);
        this.powerups.push(powerup);
        return powerup;
    }

    render(ctx) {
        for (const powerup of this.powerups) {
            powerup.render(ctx);
        }
    }

    clear() {
        this.powerups = [];
    }

    getPowerUpCount() {
        return this.powerups.length;
    }
}

// 创建全局道具管理器实例
window.powerUpManager = new PowerUpManager();
