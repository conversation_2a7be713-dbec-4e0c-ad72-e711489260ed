// 主游戏逻辑

class Game {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.gameState = 'menu'; // menu, playing, paused, gameOver
        this.lastTime = 0;
        this.deltaTime = 0;
        this.targetFPS = 60;
        this.frameTime = 1000 / this.targetFPS;
        
        // 游戏统计
        this.gameTime = 0;
        this.totalScore = 0;
        this.highScore = 0;
        
        // 性能监控
        this.fps = 0;
        this.frameCount = 0;
        this.fpsTimer = 0;
        
        // 音频上下文（预留）
        this.audioContext = null;
        this.sounds = {};
        
        this.init();
    }

    init() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');

        // 设置画布属性
        this.ctx.imageSmoothingEnabled = false;

        // 加载高分
        this.loadHighScore();

        // 检查必要的全局对象是否已加载
        this.waitForDependencies();
    }

    waitForDependencies() {
        // 检查所有必要的全局对象
        const requiredObjects = [
            'inputManager', 'collisionSystem', 'particleSystem',
            'bulletManager', 'weaponManager', 'player',
            'enemyManager', 'powerUpManager', 'levelManager'
        ];

        const checkDependencies = () => {
            const missing = requiredObjects.filter(obj => !window[obj]);

            if (missing.length === 0) {
                // 所有依赖都已加载
                this.initializeSystems();
                this.gameLoop();
                console.log('魂斗罗游戏初始化完成');
            } else {
                console.log('等待依赖加载:', missing);
                setTimeout(checkDependencies, 100);
            }
        };

        checkDependencies();
    }

    initializeSystems() {
        // 初始化碰撞组
        collisionSystem.addGroup('player');
        collisionSystem.addGroup('enemies');
        collisionSystem.addGroup('playerBullets');
        collisionSystem.addGroup('enemyBullets');
        collisionSystem.addGroup('powerups');
        collisionSystem.addGroup('obstacles');
    }

    gameLoop(currentTime = 0) {
        // 计算帧时间
        this.deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        // 限制最大帧时间，防止游戏在失去焦点后出现大跳跃
        this.deltaTime = Math.min(this.deltaTime, 1/30);
        
        // 更新FPS计数
        this.updateFPS();
        
        // 根据游戏状态更新和渲染
        switch (this.gameState) {
            case 'playing':
                this.updateGame();
                this.renderGame();
                break;
            case 'paused':
                this.renderGame();
                this.renderPauseOverlay();
                break;
            case 'gameOver':
                this.renderGame();
                this.renderGameOverOverlay();
                break;
            case 'menu':
                // 菜单状态由HTML处理
                break;
        }
        
        // 更新输入状态
        inputManager.update();
        gamepadManager.update();
        
        // 继续游戏循环
        requestAnimationFrame((time) => this.gameLoop(time));
    }

    updateFPS() {
        this.frameCount++;
        this.fpsTimer += this.deltaTime;
        
        if (this.fpsTimer >= 1.0) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.fpsTimer = 0;
        }
    }

    updateGame() {
        if (this.gameState !== 'playing') return;
        
        this.gameTime += this.deltaTime;
        
        // 检查暂停输入
        if (inputManager.isPausePressed()) {
            this.pauseGame();
            return;
        }
        
        // 检查重启输入
        if (inputManager.isRestartPressed()) {
            this.restartGame();
            return;
        }
        
        // 更新游戏对象
        this.updateGameObjects();
        
        // 处理碰撞
        this.handleCollisions();
        
        // 检查游戏结束条件
        this.checkGameOver();
    }

    updateGameObjects() {
        // 更新玩家
        if (window.player) {
            player.update(this.deltaTime);
        }

        // 更新敌人
        if (window.enemyManager) {
            enemyManager.update(this.deltaTime);
        }

        // 更新子弹
        if (window.bulletManager) {
            bulletManager.update(this.deltaTime);
        }

        // 更新道具
        if (window.powerUpManager) {
            powerUpManager.update(this.deltaTime);
        }

        // 更新粒子系统
        if (window.particleSystem) {
            particleSystem.update(this.deltaTime);
        }

        // 更新关卡
        if (window.levelManager) {
            levelManager.update(this.deltaTime);
        }
    }

    handleCollisions() {
        if (!window.collisionSystem) return;

        // 清空碰撞组
        collisionSystem.clearAll();

        // 添加对象到碰撞组
        if (window.player && player.active) {
            collisionSystem.addToGroup('player', player);
        }

        if (window.enemyManager) {
            for (const enemy of enemyManager.getEnemies()) {
                collisionSystem.addToGroup('enemies', enemy);
            }
        }

        if (window.bulletManager) {
            for (const bullet of bulletManager.getPlayerBullets()) {
                collisionSystem.addToGroup('playerBullets', bullet);
            }

            for (const bullet of bulletManager.getEnemyBullets()) {
                collisionSystem.addToGroup('enemyBullets', bullet);
            }
        }

        if (window.powerUpManager) {
            for (const powerup of powerUpManager.powerups) {
                if (powerup.active) {
                    collisionSystem.addToGroup('powerups', powerup);
                }
            }
        }
        
        // 玩家子弹 vs 敌人
        collisionSystem.checkCollisions('playerBullets', 'enemies', (bullet, enemy) => {
            const damage = bullet.onHit(enemy);
            const killed = enemy.takeDamage(damage);
            
            if (killed) {
                // 敌人死亡，可能掉落道具
                if (Math.random() < 0.3) {
                    const powerupTypes = ['health', 'score', 'weapon_machine', 'weapon_spread'];
                    const type = ArrayUtils.randomChoice(powerupTypes);
                    powerUpManager.createPowerUp(enemy.x, enemy.y, type);
                }
            }
        });
        
        // 敌人子弹 vs 玩家
        collisionSystem.checkCollisions('enemyBullets', 'player', (bullet, player) => {
            const damage = bullet.onHit(player);
            player.takeDamage(damage);
        });
        
        // 敌人 vs 玩家（近战伤害）
        collisionSystem.checkCollisions('enemies', 'player', (enemy, player) => {
            if (!player.invulnerable) {
                player.takeDamage(enemy.damage);
                
                // 击退效果
                const angle = MathUtils.angle(enemy.x, enemy.y, player.x, player.y);
                player.vx += Math.cos(angle) * 100;
                player.vy += Math.sin(angle) * 50;
            }
        });
        
        // 玩家 vs 道具（在道具系统中已处理）
    }

    checkGameOver() {
        if (!player.active || player.lives <= 0) {
            this.gameOver();
        }
    }

    renderGame() {
        // 清空画布
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 渲染关卡背景
        levelManager.render(this.ctx);
        
        // 渲染游戏对象
        this.renderGameObjects();
        
        // 渲染UI
        this.renderUI();
        
        // 渲染调试信息
        if (this.showDebugInfo) {
            this.renderDebugInfo();
        }
    }

    renderGameObjects() {
        // 渲染道具
        if (window.powerUpManager) {
            powerUpManager.render(this.ctx);
        }

        // 渲染敌人
        if (window.enemyManager) {
            enemyManager.render(this.ctx);
        }

        // 渲染玩家
        if (window.player) {
            player.render(this.ctx);
        }

        // 渲染子弹
        if (window.bulletManager) {
            bulletManager.render(this.ctx);
        }

        // 渲染粒子效果
        if (window.particleSystem) {
            particleSystem.render(this.ctx);
        }
    }

    renderUI() {
        const playerStatus = player.getStatus();
        
        // 更新HTML UI
        document.getElementById('livesCount').textContent = playerStatus.lives;
        document.getElementById('scoreValue').textContent = playerStatus.score;
        document.getElementById('weaponType').textContent = playerStatus.weapon.name;
        
        // 渲染血条
        this.renderHealthBar(playerStatus);
        
        // 渲染弹药显示
        this.renderAmmoDisplay(playerStatus.weapon);
        
        // 渲染关卡进度
        this.renderLevelProgress();
    }

    renderHealthBar(playerStatus) {
        const barWidth = 100;
        const barHeight = 10;
        const x = 10;
        const y = 50;
        
        // 背景
        this.ctx.fillStyle = '#333333';
        this.ctx.fillRect(x, y, barWidth, barHeight);
        
        // 血量
        const healthRatio = playerStatus.health / playerStatus.maxHealth;
        this.ctx.fillStyle = healthRatio > 0.5 ? '#00ff00' : healthRatio > 0.25 ? '#ffff00' : '#ff0000';
        this.ctx.fillRect(x, y, barWidth * healthRatio, barHeight);
        
        // 边框
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(x, y, barWidth, barHeight);
    }

    renderAmmoDisplay(weaponInfo) {
        if (weaponInfo.maxAmmo === -1) return; // 无限弹药
        
        const x = 10;
        const y = 70;
        
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '14px Arial';
        this.ctx.fillText(`弹药: ${weaponInfo.ammo}/${weaponInfo.maxAmmo}`, x, y);
        
        // 弹药条
        if (weaponInfo.maxAmmo > 0) {
            const barWidth = 80;
            const barHeight = 6;
            const ammoRatio = weaponInfo.ammo / weaponInfo.maxAmmo;
            
            this.ctx.fillStyle = '#333333';
            this.ctx.fillRect(x, y + 5, barWidth, barHeight);
            
            this.ctx.fillStyle = ammoRatio > 0.3 ? '#00ff00' : '#ff0000';
            this.ctx.fillRect(x, y + 5, barWidth * ammoRatio, barHeight);
        }
        
        // 重装进度
        if (weaponInfo.isReloading) {
            const reloadProgress = weaponInfo.reloadProgress;
            this.ctx.fillStyle = '#ffff00';
            this.ctx.fillRect(x, y + 15, 80 * reloadProgress, 4);
            
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = '12px Arial';
            this.ctx.fillText('重装中...', x, y + 30);
        }
    }

    renderLevelProgress() {
        const level = levelManager.getCurrentLevel();
        if (!level) return;
        
        const progress = level.getProgress();
        const x = this.canvas.width - 200;
        const y = 20;
        
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'right';
        
        this.ctx.fillText(`关卡: ${levelManager.getLevelNumber()}`, x, y);
        this.ctx.fillText(`敌人: ${progress.enemiesKilled}/${progress.enemiesRequired}`, x, y + 20);
        this.ctx.fillText(`时间: ${TimeUtils.formatTime(progress.timeElapsed * 1000)}`, x, y + 40);
        
        this.ctx.textAlign = 'left';
    }

    renderPauseOverlay() {
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 48px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('游戏暂停', this.canvas.width / 2, this.canvas.height / 2);
        
        this.ctx.font = '24px Arial';
        this.ctx.fillText('按 P 键继续', this.canvas.width / 2, this.canvas.height / 2 + 50);
        
        this.ctx.restore();
    }

    renderGameOverOverlay() {
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.ctx.fillStyle = '#ff0000';
        this.ctx.font = 'bold 48px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('游戏结束', this.canvas.width / 2, this.canvas.height / 2 - 50);
        
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '24px Arial';
        this.ctx.fillText(`最终得分: ${player.score}`, this.canvas.width / 2, this.canvas.height / 2);
        this.ctx.fillText(`最高得分: ${this.highScore}`, this.canvas.width / 2, this.canvas.height / 2 + 30);
        this.ctx.fillText('按 R 键重新开始', this.canvas.width / 2, this.canvas.height / 2 + 80);
        
        this.ctx.restore();
    }

    renderDebugInfo() {
        const x = 10;
        let y = this.canvas.height - 100;
        
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(x - 5, y - 60, 200, 80);
        
        this.ctx.fillStyle = '#00ff00';
        this.ctx.font = '12px monospace';
        this.ctx.textAlign = 'left';
        
        this.ctx.fillText(`FPS: ${this.fps}`, x, y);
        this.ctx.fillText(`Enemies: ${enemyManager.getEnemyCount()}`, x, y + 15);
        this.ctx.fillText(`Bullets: ${bulletManager.getBulletCount()}`, x, y + 30);
        this.ctx.fillText(`Particles: ${particleSystem.getParticleCount()}`, x, y + 45);
    }

    // 游戏状态控制方法
    startGame() {
        this.gameState = 'playing';
        this.gameTime = 0;
        
        // 重置玩家
        player.x = 100;
        player.y = 300;
        player.health = player.maxHealth;
        player.lives = 3;
        player.score = 0;
        player.active = true;
        
        // 开始第一关
        levelManager.startLevel(1);
        
        // 隐藏菜单，显示游戏界面
        document.getElementById('startMenu').classList.add('hidden');
        document.getElementById('gameScreen').classList.remove('hidden');
        
        console.log('游戏开始');
    }

    pauseGame() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            document.getElementById('pauseMenu').classList.remove('hidden');
        }
    }

    resumeGame() {
        if (this.gameState === 'paused') {
            this.gameState = 'playing';
            document.getElementById('pauseMenu').classList.add('hidden');
        }
    }

    restartGame() {
        this.startGame();
        if (this.gameState === 'paused') {
            document.getElementById('pauseMenu').classList.add('hidden');
        }
        if (this.gameState === 'gameOver') {
            document.getElementById('gameOverMenu').classList.add('hidden');
        }
    }

    gameOver() {
        this.gameState = 'gameOver';
        
        // 更新高分
        if (player.score > this.highScore) {
            this.highScore = player.score;
            this.saveHighScore();
        }
        
        // 显示游戏结束界面
        document.getElementById('finalScoreValue').textContent = player.score;
        document.getElementById('gameOverMenu').classList.remove('hidden');
        
        console.log('游戏结束，得分:', player.score);
    }

    returnToMenu() {
        this.gameState = 'menu';
        
        // 隐藏所有游戏界面
        document.getElementById('gameScreen').classList.add('hidden');
        document.getElementById('pauseMenu').classList.add('hidden');
        document.getElementById('gameOverMenu').classList.add('hidden');
        document.getElementById('startMenu').classList.remove('hidden');
        
        // 清理游戏状态
        enemyManager.clear();
        bulletManager.clear();
        powerUpManager.clear();
        particleSystem.clear();
    }

    // 数据持久化
    saveHighScore() {
        localStorage.setItem('contraHighScore', this.highScore.toString());
    }

    loadHighScore() {
        const saved = localStorage.getItem('contraHighScore');
        this.highScore = saved ? parseInt(saved) : 0;
    }

    // 调试功能
    toggleDebugInfo() {
        this.showDebugInfo = !this.showDebugInfo;
    }
}

// 创建全局游戏实例
window.game = new Game();
