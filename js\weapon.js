// 武器系统

class Weapon {
    constructor(type = 'normal') {
        this.type = type;
        this.fireRate = 0.2; // 射击间隔（秒）
        this.lastFireTime = 0;
        this.damage = 1;
        this.bulletSpeed = 300;
        this.bulletColor = '#ffff00';
        this.bulletType = 'normal';
        this.ammo = -1; // -1 表示无限弹药
        this.maxAmmo = -1;
        this.reloadTime = 0;
        this.isReloading = false;
        this.reloadStartTime = 0;
        this.spread = 0; // 散射角度
        this.bulletCount = 1; // 每次射击的子弹数量
        this.piercing = false;
        this.maxHits = 1;
        
        this.setupWeaponStats();
    }

    setupWeaponStats() {
        switch (this.type) {
            case 'normal':
                this.fireRate = 0.15;
                this.damage = 1;
                this.bulletSpeed = 400;
                this.bulletColor = '#ffff00';
                this.bulletType = 'normal';
                break;
                
            case 'machine':
                this.fireRate = 0.08;
                this.damage = 1;
                this.bulletSpeed = 450;
                this.bulletColor = '#ff8800';
                this.bulletType = 'normal';
                break;
                
            case 'spread':
                this.fireRate = 0.3;
                this.damage = 1;
                this.bulletSpeed = 350;
                this.bulletColor = '#00ff00';
                this.bulletType = 'spread';
                this.bulletCount = 3;
                this.spread = Math.PI / 8;
                break;
                
            case 'laser':
                this.fireRate = 0.05;
                this.damage = 2;
                this.bulletSpeed = 600;
                this.bulletColor = '#ff0088';
                this.bulletType = 'laser';
                this.piercing = true;
                this.maxHits = 3;
                break;
                
            case 'rocket':
                this.fireRate = 0.8;
                this.damage = 5;
                this.bulletSpeed = 250;
                this.bulletColor = '#ff4400';
                this.bulletType = 'rocket';
                this.ammo = 10;
                this.maxAmmo = 10;
                this.reloadTime = 2;
                break;
                
            case 'flame':
                this.fireRate = 0.05;
                this.damage = 1;
                this.bulletSpeed = 200;
                this.bulletColor = '#ff6600';
                this.bulletType = 'normal';
                this.bulletCount = 5;
                this.spread = Math.PI / 4;
                this.ammo = 100;
                this.maxAmmo = 100;
                this.reloadTime = 1.5;
                break;
        }
    }

    canFire(currentTime) {
        if (this.isReloading) {
            return false;
        }
        
        if (this.ammo === 0) {
            this.startReload(currentTime);
            return false;
        }
        
        return currentTime - this.lastFireTime >= this.fireRate;
    }

    fire(x, y, angle, currentTime) {
        if (!this.canFire(currentTime)) {
            return [];
        }

        this.lastFireTime = currentTime;
        
        if (this.ammo > 0) {
            this.ammo--;
        }

        const bullets = [];
        const bulletConfig = {
            damage: this.damage,
            speed: this.bulletSpeed,
            color: this.bulletColor,
            owner: 'player',
            type: this.bulletType,
            piercing: this.piercing,
            maxHits: this.maxHits
        };

        switch (this.type) {
            case 'spread':
            case 'flame':
                // 扩散射击
                const angleStep = this.spread / (this.bulletCount - 1);
                const startAngle = angle - this.spread / 2;
                
                for (let i = 0; i < this.bulletCount; i++) {
                    const bulletAngle = startAngle + angleStep * i;
                    const bullet = bulletManager.createBullet(x, y, bulletAngle, bulletConfig);
                    bullets.push(bullet);
                }
                break;
                
            case 'rocket':
                // 火箭弹
                const rocket = bulletManager.createBullet(x, y, angle, {
                    ...bulletConfig,
                    width: 6,
                    height: 12
                });
                bullets.push(rocket);
                
                // 创建发射烟雾
                particleSystem.createSmoke(x, y, 8);
                break;
                
            default:
                // 普通射击
                const bullet = bulletManager.createBullet(x, y, angle, bulletConfig);
                bullets.push(bullet);
        }

        // 创建枪口火焰效果
        this.createMuzzleFlash(x, y, angle);
        
        return bullets;
    }

    createMuzzleFlash(x, y, angle) {
        const flashConfig = {
            count: 5,
            minSpeed: 50,
            maxSpeed: 100,
            minSize: 2,
            maxSize: 4,
            life: 0.1,
            colors: ['#ffff44', '#ff8844', '#ffffff'],
            gravity: 0,
            friction: 0.9,
            type: 'spark'
        };

        // 在枪口位置创建火花
        const muzzleX = x + Math.cos(angle) * 20;
        const muzzleY = y + Math.sin(angle) * 20;
        
        for (let i = 0; i < flashConfig.count; i++) {
            const particle = particleSystem.pool.get();
            const spread = Math.PI / 6;
            const particleAngle = angle + MathUtils.randomFloat(-spread/2, spread/2);
            const speed = MathUtils.randomFloat(flashConfig.minSpeed, flashConfig.maxSpeed);

            particle.x = muzzleX;
            particle.y = muzzleY;
            particle.vx = Math.cos(particleAngle) * speed;
            particle.vy = Math.sin(particleAngle) * speed;
            particle.size = MathUtils.randomFloat(flashConfig.minSize, flashConfig.maxSize);
            particle.life = flashConfig.life;
            particle.maxLife = flashConfig.life;
            particle.color = ArrayUtils.randomChoice(flashConfig.colors);
            particle.gravity = flashConfig.gravity;
            particle.friction = flashConfig.friction;
            particle.type = flashConfig.type;
            particle.active = true;

            particleSystem.particles.push(particle);
        }
    }

    startReload(currentTime) {
        if (this.maxAmmo === -1 || this.isReloading) {
            return;
        }
        
        this.isReloading = true;
        this.reloadStartTime = currentTime;
    }

    update(currentTime) {
        if (this.isReloading) {
            if (currentTime - this.reloadStartTime >= this.reloadTime) {
                this.ammo = this.maxAmmo;
                this.isReloading = false;
            }
        }
    }

    getReloadProgress() {
        if (!this.isReloading) return 1;
        
        const elapsed = Date.now() / 1000 - this.reloadStartTime;
        return Math.min(elapsed / this.reloadTime, 1);
    }

    getAmmoRatio() {
        if (this.maxAmmo === -1) return 1;
        return this.ammo / this.maxAmmo;
    }

    getName() {
        const names = {
            'normal': '普通枪',
            'machine': '机关枪',
            'spread': '散弹枪',
            'laser': '激光枪',
            'rocket': '火箭筒',
            'flame': '火焰枪'
        };
        return names[this.type] || '未知武器';
    }

    getDescription() {
        const descriptions = {
            'normal': '标准武器，平衡的射速和伤害',
            'machine': '高射速，低伤害',
            'spread': '扇形射击，近距离威力大',
            'laser': '高伤害，可穿透敌人',
            'rocket': '爆炸伤害，弹药有限',
            'flame': '火焰攻击，持续伤害'
        };
        return descriptions[this.type] || '未知武器';
    }
}

class WeaponManager {
    constructor() {
        this.currentWeapon = new Weapon('normal');
        this.availableWeapons = ['normal'];
        this.weaponIndex = 0;
    }

    switchWeapon(type) {
        if (this.availableWeapons.includes(type)) {
            this.currentWeapon = new Weapon(type);
            this.weaponIndex = this.availableWeapons.indexOf(type);
        }
    }

    nextWeapon() {
        this.weaponIndex = (this.weaponIndex + 1) % this.availableWeapons.length;
        this.currentWeapon = new Weapon(this.availableWeapons[this.weaponIndex]);
    }

    previousWeapon() {
        this.weaponIndex = (this.weaponIndex - 1 + this.availableWeapons.length) % this.availableWeapons.length;
        this.currentWeapon = new Weapon(this.availableWeapons[this.weaponIndex]);
    }

    addWeapon(type) {
        if (!this.availableWeapons.includes(type)) {
            this.availableWeapons.push(type);
        }
    }

    fire(x, y, angle) {
        const currentTime = Date.now() / 1000;
        return this.currentWeapon.fire(x, y, angle, currentTime);
    }

    update() {
        const currentTime = Date.now() / 1000;
        this.currentWeapon.update(currentTime);
    }

    canFire() {
        const currentTime = Date.now() / 1000;
        return this.currentWeapon.canFire(currentTime);
    }

    getCurrentWeapon() {
        return this.currentWeapon;
    }

    getWeaponInfo() {
        return {
            name: this.currentWeapon.getName(),
            type: this.currentWeapon.type,
            ammo: this.currentWeapon.ammo,
            maxAmmo: this.currentWeapon.maxAmmo,
            isReloading: this.currentWeapon.isReloading,
            reloadProgress: this.currentWeapon.getReloadProgress(),
            ammoRatio: this.currentWeapon.getAmmoRatio()
        };
    }
}

// 创建全局武器管理器实例
window.weaponManager = new WeaponManager();
