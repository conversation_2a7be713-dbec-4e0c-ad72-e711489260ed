// 玩家角色系统

class Player {
    constructor(x = 100, y = 300) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 32;
        this.vx = 0;
        this.vy = 0;
        this.speed = 200;
        this.jumpSpeed = 300;
        this.gravity = 800;
        this.onGround = false;
        this.canJump = true;
        
        // 生命值和状态
        this.maxHealth = 3;
        this.health = this.maxHealth;
        this.lives = 3;
        this.invulnerable = false;
        this.invulnerabilityTime = 1.5;
        this.invulnerabilityTimer = 0;
        
        // 动画状态
        this.facing = 1; // 1 = 右, -1 = 左
        this.animationState = 'idle';
        this.animationTime = 0;
        this.frameIndex = 0;
        
        // 武器系统
        this.weaponManager = new WeaponManager();
        
        // 碰撞
        this.collisionType = 'rect';
        this.active = true;
        
        // 特殊能力
        this.canDoubleJump = false;
        this.hasDoubleJumped = false;
        this.dashCooldown = 0;
        this.dashDuration = 0.2;
        this.dashSpeed = 400;
        this.isDashing = false;
        this.dashTimer = 0;
        
        // 分数
        this.score = 0;
    }

    update(deltaTime) {
        if (!this.active) return;

        // 更新武器
        this.weaponManager.update();
        
        // 更新无敌时间
        if (this.invulnerable) {
            this.invulnerabilityTimer -= deltaTime;
            if (this.invulnerabilityTimer <= 0) {
                this.invulnerable = false;
            }
        }
        
        // 更新冲刺
        if (this.isDashing) {
            this.dashTimer -= deltaTime;
            if (this.dashTimer <= 0) {
                this.isDashing = false;
            }
        }
        
        if (this.dashCooldown > 0) {
            this.dashCooldown -= deltaTime;
        }

        // 处理输入
        this.handleInput(deltaTime);
        
        // 更新物理
        this.updatePhysics(deltaTime);
        
        // 更新动画
        this.updateAnimation(deltaTime);
        
        // 边界检查
        this.checkBounds();
    }

    handleInput(deltaTime) {
        const input = inputManager.getMovementInput();
        
        // 水平移动
        if (!this.isDashing) {
            this.vx = input.x * this.speed;
            
            // 更新朝向
            if (input.x > 0) {
                this.facing = 1;
            } else if (input.x < 0) {
                this.facing = -1;
            }
        }
        
        // 跳跃
        if (inputManager.isKeyPressed('KeyW') || inputManager.isKeyPressed('ArrowUp')) {
            this.jump();
        }
        
        // 射击
        if (inputManager.isShootPressed()) {
            this.shoot();
        }
        
        // 冲刺
        if (inputManager.isKeyPressed('KeyX') && this.dashCooldown <= 0 && !this.isDashing) {
            this.dash();
        }
        
        // 切换武器
        if (inputManager.isKeyPressed('KeyQ')) {
            this.weaponManager.previousWeapon();
        }
        if (inputManager.isKeyPressed('KeyE')) {
            this.weaponManager.nextWeapon();
        }
    }

    updatePhysics(deltaTime) {
        // 重力
        if (!this.onGround && !this.isDashing) {
            this.vy += this.gravity * deltaTime;
        }
        
        // 冲刺物理
        if (this.isDashing) {
            this.vx = this.facing * this.dashSpeed;
            this.vy = 0;
        }
        
        // 更新位置
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;
        
        // 简单的地面检测（假设地面在y=550）
        const groundY = 550;
        if (this.y + this.height/2 >= groundY) {
            this.y = groundY - this.height/2;
            this.vy = 0;
            this.onGround = true;
            this.canJump = true;
            this.hasDoubleJumped = false;
        } else {
            this.onGround = false;
        }
    }

    updateAnimation(deltaTime) {
        this.animationTime += deltaTime;
        
        // 确定动画状态
        if (this.isDashing) {
            this.animationState = 'dash';
        } else if (!this.onGround) {
            this.animationState = 'jump';
        } else if (Math.abs(this.vx) > 10) {
            this.animationState = 'run';
        } else {
            this.animationState = 'idle';
        }
        
        // 更新帧索引
        const frameRate = 8; // 每秒8帧
        this.frameIndex = Math.floor(this.animationTime * frameRate) % this.getFrameCount();
    }

    getFrameCount() {
        switch (this.animationState) {
            case 'idle': return 4;
            case 'run': return 6;
            case 'jump': return 2;
            case 'dash': return 3;
            default: return 1;
        }
    }

    jump() {
        if (this.canJump && this.onGround) {
            this.vy = -this.jumpSpeed;
            this.onGround = false;
            this.canJump = false;
        } else if (this.canDoubleJump && !this.hasDoubleJumped && !this.onGround) {
            this.vy = -this.jumpSpeed * 0.8;
            this.hasDoubleJumped = true;
            
            // 创建二段跳特效
            particleSystem.createSparks(this.x, this.y + this.height/2, 8, {
                colors: ['#00ffff', '#ffffff'],
                minSpeed: 80,
                maxSpeed: 120
            });
        }
    }

    dash() {
        this.isDashing = true;
        this.dashTimer = this.dashDuration;
        this.dashCooldown = 1.0; // 1秒冷却
        
        // 创建冲刺特效
        particleSystem.createSparks(this.x - this.facing * 15, this.y, 10, {
            colors: ['#ffff00', '#ffffff'],
            minSpeed: 100,
            maxSpeed: 150
        });
    }

    shoot() {
        if (this.weaponManager.canFire()) {
            // 计算射击位置和角度
            const shootX = this.x + this.facing * 15;
            const shootY = this.y - 5;
            const angle = this.facing > 0 ? 0 : Math.PI;
            
            this.weaponManager.fire(shootX, shootY, angle);
        }
    }

    takeDamage(damage = 1) {
        if (this.invulnerable) return false;
        
        this.health -= damage;
        this.invulnerable = true;
        this.invulnerabilityTimer = this.invulnerabilityTime;
        
        // 创建受伤特效
        particleSystem.createBlood(this.x, this.y, Math.PI);
        
        if (this.health <= 0) {
            this.die();
        }
        
        return true;
    }

    die() {
        this.lives--;
        
        // 创建死亡爆炸特效
        particleSystem.createExplosion(this.x, this.y, 30, {
            colors: ['#ff0000', '#ff4400', '#ffff00'],
            minSpeed: 100,
            maxSpeed: 200
        });
        
        if (this.lives > 0) {
            // 重生
            this.respawn();
        } else {
            // 游戏结束
            this.active = false;
        }
    }

    respawn() {
        this.health = this.maxHealth;
        this.x = 100;
        this.y = 300;
        this.vx = 0;
        this.vy = 0;
        this.invulnerable = true;
        this.invulnerabilityTimer = 2.0; // 重生后2秒无敌
    }

    heal(amount = 1) {
        this.health = Math.min(this.health + amount, this.maxHealth);
    }

    addLife() {
        this.lives++;
    }

    addScore(points) {
        this.score += points;
    }

    checkBounds() {
        // 左右边界
        if (this.x - this.width/2 < 0) {
            this.x = this.width/2;
        }
        if (this.x + this.width/2 > 800) {
            this.x = 800 - this.width/2;
        }
        
        // 上边界
        if (this.y - this.height/2 < 0) {
            this.y = this.height/2;
            this.vy = 0;
        }
        
        // 下边界（死亡）
        if (this.y > 650) {
            this.takeDamage(this.health); // 立即死亡
        }
    }

    render(ctx) {
        if (!this.active) return;

        ctx.save();
        
        // 无敌闪烁效果
        if (this.invulnerable) {
            const blinkRate = 10;
            if (Math.floor(this.invulnerabilityTimer * blinkRate) % 2 === 0) {
                ctx.globalAlpha = 0.5;
            }
        }
        
        // 冲刺残影效果
        if (this.isDashing) {
            ctx.globalAlpha = 0.8;
        }
        
        ctx.translate(this.x, this.y);
        ctx.scale(this.facing, 1);
        
        // 渲染玩家（简单矩形，可以后续替换为精灵图）
        this.renderSprite(ctx);
        
        ctx.restore();
        
        // 渲染UI元素
        this.renderUI(ctx);
    }

    renderSprite(ctx) {
        // 主体
        ctx.fillStyle = this.invulnerable ? '#ff8888' : '#00ff00';
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        
        // 头部
        ctx.fillStyle = '#ffddaa';
        ctx.fillRect(-this.width/3, -this.height/2, this.width*2/3, this.height/3);
        
        // 武器指示
        const weapon = this.weaponManager.getCurrentWeapon();
        ctx.fillStyle = weapon.bulletColor;
        ctx.fillRect(this.width/2 - 2, -2, 8, 4);
        
        // 动画效果（简单的颜色变化）
        if (this.animationState === 'run') {
            const intensity = Math.sin(this.animationTime * 10) * 0.2 + 0.8;
            ctx.globalAlpha = intensity;
        }
    }

    renderUI(ctx) {
        // 这里可以添加玩家相关的UI元素
        // 比如血条、弹药显示等
    }

    getBounds() {
        return {
            left: this.x - this.width/2,
            right: this.x + this.width/2,
            top: this.y - this.height/2,
            bottom: this.y + this.height/2
        };
    }

    // 获取玩家状态信息
    getStatus() {
        return {
            health: this.health,
            maxHealth: this.maxHealth,
            lives: this.lives,
            score: this.score,
            weapon: this.weaponManager.getWeaponInfo(),
            position: { x: this.x, y: this.y },
            invulnerable: this.invulnerable
        };
    }
}

// 创建全局玩家实例
window.player = new Player();
