// 关卡系统

class Level {
    constructor(levelNumber = 1) {
        this.levelNumber = levelNumber;
        this.background = null;
        this.platforms = [];
        this.obstacles = [];
        this.spawnPoints = [];
        this.backgroundLayers = [];
        this.scrollSpeed = 50;
        this.scrollOffset = 0;
        this.width = 1600; // 关卡宽度
        this.height = 600;
        
        // 关卡目标
        this.enemiesRequired = 10;
        this.enemiesKilled = 0;
        this.completed = false;
        this.timeLimit = 300; // 5分钟
        this.timeElapsed = 0;
        
        // 环境效果
        this.weather = 'clear';
        this.ambientParticles = [];
        
        this.generateLevel();
    }

    generateLevel() {
        // 根据关卡数生成不同的环境
        this.generateBackground();
        this.generatePlatforms();
        this.generateObstacles();
        this.generateSpawnPoints();
        this.setupLevelParameters();
    }

    generateBackground() {
        // 创建多层背景
        this.backgroundLayers = [
            {
                color: this.getSkyColor(),
                speed: 0.1,
                offset: 0
            },
            {
                color: this.getMountainColor(),
                speed: 0.3,
                offset: 0,
                type: 'mountains'
            },
            {
                color: this.getGroundColor(),
                speed: 0.5,
                offset: 0,
                type: 'buildings'
            }
        ];
    }

    getSkyColor() {
        const colors = [
            '#87CEEB', // 天蓝色
            '#FF6347', // 黄昏
            '#2F4F4F', // 夜晚
            '#8B0000'  // 战争红
        ];
        return colors[Math.min(this.levelNumber - 1, colors.length - 1)];
    }

    getMountainColor() {
        const colors = [
            '#696969',
            '#8B4513',
            '#2F2F2F',
            '#4B0000'
        ];
        return colors[Math.min(this.levelNumber - 1, colors.length - 1)];
    }

    getGroundColor() {
        const colors = [
            '#228B22',
            '#8B4513',
            '#2F2F2F',
            '#800000'
        ];
        return colors[Math.min(this.levelNumber - 1, colors.length - 1)];
    }

    generatePlatforms() {
        this.platforms = [];
        
        // 主地面
        this.platforms.push({
            x: 0,
            y: 550,
            width: this.width,
            height: 50,
            type: 'ground'
        });
        
        // 根据关卡生成平台
        const platformCount = 3 + this.levelNumber;
        for (let i = 0; i < platformCount; i++) {
            this.platforms.push({
                x: MathUtils.randomFloat(100, this.width - 200),
                y: MathUtils.randomFloat(200, 450),
                width: MathUtils.randomFloat(80, 150),
                height: 20,
                type: 'platform'
            });
        }
    }

    generateObstacles() {
        this.obstacles = [];
        
        // 根据关卡生成障碍物
        const obstacleCount = this.levelNumber * 2;
        for (let i = 0; i < obstacleCount; i++) {
            const type = ArrayUtils.randomChoice(['wall', 'spike', 'barrel']);
            this.obstacles.push({
                x: MathUtils.randomFloat(200, this.width - 200),
                y: 520,
                width: type === 'wall' ? 30 : 20,
                height: type === 'wall' ? 60 : 30,
                type: type,
                destructible: type === 'barrel'
            });
        }
    }

    generateSpawnPoints() {
        this.spawnPoints = [];
        
        // 敌人生成点
        const spawnCount = 4 + this.levelNumber;
        for (let i = 0; i < spawnCount; i++) {
            this.spawnPoints.push({
                x: MathUtils.randomFloat(100, this.width - 100),
                y: 500,
                type: 'enemy',
                cooldown: 0,
                maxCooldown: MathUtils.randomFloat(3, 8)
            });
        }
    }

    setupLevelParameters() {
        // 根据关卡调整参数
        this.enemiesRequired = 8 + this.levelNumber * 3;
        this.timeLimit = 240 + this.levelNumber * 60;
        
        // 设置天气效果
        if (this.levelNumber >= 3) {
            this.weather = ArrayUtils.randomChoice(['rain', 'snow', 'fog']);
        }
    }

    update(deltaTime) {
        this.timeElapsed += deltaTime;
        
        // 更新背景滚动
        this.updateBackgroundScroll(deltaTime);
        
        // 更新环境效果
        this.updateWeatherEffects(deltaTime);
        
        // 更新生成点
        this.updateSpawnPoints(deltaTime);
        
        // 检查关卡完成条件
        this.checkLevelCompletion();
    }

    updateBackgroundScroll(deltaTime) {
        // 根据玩家位置滚动背景
        const targetOffset = player.x - 400;
        this.scrollOffset = MathUtils.lerp(this.scrollOffset, targetOffset, deltaTime * 2);
        
        // 更新背景层偏移
        for (const layer of this.backgroundLayers) {
            layer.offset = this.scrollOffset * layer.speed;
        }
    }

    updateWeatherEffects(deltaTime) {
        switch (this.weather) {
            case 'rain':
                this.createRainEffect();
                break;
            case 'snow':
                this.createSnowEffect();
                break;
            case 'fog':
                // 雾效果在渲染时处理
                break;
        }
    }

    createRainEffect() {
        if (Math.random() < 0.3) {
            particleSystem.particles.push({
                x: MathUtils.randomFloat(-100, 900),
                y: -10,
                vx: -20,
                vy: 400,
                size: 1,
                life: 2,
                maxLife: 2,
                color: '#4444ff',
                gravity: 0,
                friction: 1,
                type: 'spark',
                active: true,
                alpha: 0.6
            });
        }
    }

    createSnowEffect() {
        if (Math.random() < 0.1) {
            particleSystem.particles.push({
                x: MathUtils.randomFloat(-100, 900),
                y: -10,
                vx: MathUtils.randomFloat(-10, 10),
                vy: MathUtils.randomFloat(30, 60),
                size: MathUtils.randomFloat(2, 4),
                life: 10,
                maxLife: 10,
                color: '#ffffff',
                gravity: 0,
                friction: 0.99,
                type: 'circle',
                active: true,
                alpha: 0.8
            });
        }
    }

    updateSpawnPoints(deltaTime) {
        for (const spawnPoint of this.spawnPoints) {
            spawnPoint.cooldown -= deltaTime;
            
            if (spawnPoint.cooldown <= 0 && enemyManager.getEnemyCount() < 8) {
                this.spawnEnemyAtPoint(spawnPoint);
                spawnPoint.cooldown = spawnPoint.maxCooldown;
            }
        }
    }

    spawnEnemyAtPoint(spawnPoint) {
        const enemyTypes = ['soldier', 'fast'];
        if (this.levelNumber >= 2) enemyTypes.push('heavy');
        if (this.levelNumber >= 3) enemyTypes.push('sniper');
        if (this.levelNumber >= 4) enemyTypes.push('boss');
        
        const enemyType = ArrayUtils.randomChoice(enemyTypes);
        const enemy = new Enemy(spawnPoint.x, spawnPoint.y, enemyType);
        enemyManager.enemies.push(enemy);
    }

    checkLevelCompletion() {
        // 检查是否完成关卡
        if (enemyManager.enemiesKilled >= this.enemiesRequired) {
            this.completed = true;
        }
        
        // 检查时间限制
        if (this.timeElapsed >= this.timeLimit) {
            // 时间到，游戏失败
            // 这里可以触发游戏结束事件
        }
    }

    render(ctx) {
        // 渲染背景
        this.renderBackground(ctx);
        
        // 渲染平台和障碍物
        this.renderPlatforms(ctx);
        this.renderObstacles(ctx);
        
        // 渲染天气效果
        this.renderWeatherEffects(ctx);
    }

    renderBackground(ctx) {
        // 渲染天空
        const gradient = ctx.createLinearGradient(0, 0, 0, 600);
        gradient.addColorStop(0, this.getSkyColor());
        gradient.addColorStop(1, ColorUtils.lerpColor(this.getSkyColor(), '#000000', 0.3));
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 800, 600);
        
        // 渲染背景层
        for (const layer of this.backgroundLayers) {
            this.renderBackgroundLayer(ctx, layer);
        }
    }

    renderBackgroundLayer(ctx, layer) {
        ctx.save();
        ctx.translate(-layer.offset, 0);
        
        if (layer.type === 'mountains') {
            this.renderMountains(ctx, layer.color);
        } else if (layer.type === 'buildings') {
            this.renderBuildings(ctx, layer.color);
        }
        
        ctx.restore();
    }

    renderMountains(ctx, color) {
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.moveTo(0, 400);
        
        for (let x = 0; x <= this.width; x += 100) {
            const height = 300 + Math.sin(x * 0.01) * 50;
            ctx.lineTo(x, height);
        }
        
        ctx.lineTo(this.width, 600);
        ctx.lineTo(0, 600);
        ctx.closePath();
        ctx.fill();
    }

    renderBuildings(ctx, color) {
        ctx.fillStyle = color;
        
        for (let x = 0; x < this.width; x += 80) {
            const buildingHeight = MathUtils.randomFloat(100, 200);
            ctx.fillRect(x, 600 - buildingHeight, 60, buildingHeight);
            
            // 窗户
            ctx.fillStyle = '#ffff00';
            for (let y = 600 - buildingHeight + 20; y < 580; y += 30) {
                for (let wx = x + 10; wx < x + 50; wx += 15) {
                    if (Math.random() > 0.3) {
                        ctx.fillRect(wx, y, 8, 12);
                    }
                }
            }
            ctx.fillStyle = color;
        }
    }

    renderPlatforms(ctx) {
        ctx.fillStyle = '#8B4513';
        
        for (const platform of this.platforms) {
            const screenX = platform.x - this.scrollOffset;
            if (screenX > -platform.width && screenX < 800 + platform.width) {
                ctx.fillRect(screenX, platform.y, platform.width, platform.height);
                
                // 平台边缘高光
                ctx.fillStyle = '#CD853F';
                ctx.fillRect(screenX, platform.y, platform.width, 5);
                ctx.fillStyle = '#8B4513';
            }
        }
    }

    renderObstacles(ctx) {
        for (const obstacle of this.obstacles) {
            const screenX = obstacle.x - this.scrollOffset;
            if (screenX > -obstacle.width && screenX < 800 + obstacle.width) {
                this.renderObstacle(ctx, obstacle, screenX);
            }
        }
    }

    renderObstacle(ctx, obstacle, screenX) {
        switch (obstacle.type) {
            case 'wall':
                ctx.fillStyle = '#666666';
                ctx.fillRect(screenX, obstacle.y, obstacle.width, obstacle.height);
                break;
                
            case 'spike':
                ctx.fillStyle = '#444444';
                ctx.beginPath();
                ctx.moveTo(screenX, obstacle.y + obstacle.height);
                ctx.lineTo(screenX + obstacle.width/2, obstacle.y);
                ctx.lineTo(screenX + obstacle.width, obstacle.y + obstacle.height);
                ctx.closePath();
                ctx.fill();
                break;
                
            case 'barrel':
                ctx.fillStyle = obstacle.destructible ? '#8B4513' : '#666666';
                ctx.fillRect(screenX, obstacle.y, obstacle.width, obstacle.height);
                ctx.strokeStyle = '#000000';
                ctx.lineWidth = 2;
                ctx.strokeRect(screenX, obstacle.y, obstacle.width, obstacle.height);
                break;
        }
    }

    renderWeatherEffects(ctx) {
        if (this.weather === 'fog') {
            ctx.save();
            ctx.globalAlpha = 0.3;
            ctx.fillStyle = '#cccccc';
            ctx.fillRect(0, 0, 800, 600);
            ctx.restore();
        }
    }

    // 碰撞检测相关方法
    checkPlatformCollision(object) {
        for (const platform of this.platforms) {
            if (this.isObjectOnPlatform(object, platform)) {
                return platform;
            }
        }
        return null;
    }

    isObjectOnPlatform(object, platform) {
        return object.x + object.width/2 > platform.x &&
               object.x - object.width/2 < platform.x + platform.width &&
               object.y + object.height/2 >= platform.y &&
               object.y + object.height/2 <= platform.y + platform.height;
    }

    getProgress() {
        return {
            enemiesKilled: enemyManager.enemiesKilled,
            enemiesRequired: this.enemiesRequired,
            timeElapsed: this.timeElapsed,
            timeLimit: this.timeLimit,
            completed: this.completed
        };
    }
}

class LevelManager {
    constructor() {
        this.currentLevel = null;
        this.levelNumber = 1;
        this.transitioning = false;
        this.transitionTimer = 0;
        this.transitionDuration = 2;
    }

    startLevel(levelNumber) {
        this.levelNumber = levelNumber;
        this.currentLevel = new Level(levelNumber);
        
        // 重置游戏状态
        enemyManager.clear();
        bulletManager.clear();
        powerUpManager.clear();
        particleSystem.clear();
        
        // 重置敌人击杀计数
        enemyManager.enemiesKilled = 0;
        enemyManager.waveNumber = levelNumber;
    }

    update(deltaTime) {
        if (this.transitioning) {
            this.transitionTimer += deltaTime;
            if (this.transitionTimer >= this.transitionDuration) {
                this.transitioning = false;
                this.startLevel(this.levelNumber + 1);
            }
            return;
        }

        if (this.currentLevel) {
            this.currentLevel.update(deltaTime);
            
            // 检查关卡完成
            if (this.currentLevel.completed && !this.transitioning) {
                this.startTransition();
            }
        }
    }

    startTransition() {
        this.transitioning = true;
        this.transitionTimer = 0;
        
        // 创建关卡完成特效
        for (let i = 0; i < 50; i++) {
            particleSystem.createSparks(
                MathUtils.randomFloat(0, 800),
                MathUtils.randomFloat(0, 600),
                1,
                {
                    colors: ['#ffff00', '#00ff00', '#00ffff'],
                    life: 3,
                    minSpeed: 50,
                    maxSpeed: 100
                }
            );
        }
    }

    render(ctx) {
        if (this.currentLevel) {
            this.currentLevel.render(ctx);
        }
        
        // 渲染过渡效果
        if (this.transitioning) {
            this.renderTransition(ctx);
        }
    }

    renderTransition(ctx) {
        const progress = this.transitionTimer / this.transitionDuration;
        
        ctx.save();
        ctx.globalAlpha = Math.sin(progress * Math.PI) * 0.8;
        ctx.fillStyle = '#000000';
        ctx.fillRect(0, 0, 800, 600);
        
        // 显示关卡完成文字
        ctx.globalAlpha = 1;
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`关卡 ${this.levelNumber} 完成!`, 400, 250);
        
        ctx.font = 'bold 24px Arial';
        ctx.fillText(`准备进入关卡 ${this.levelNumber + 1}`, 400, 350);
        
        ctx.restore();
    }

    getCurrentLevel() {
        return this.currentLevel;
    }

    getLevelNumber() {
        return this.levelNumber;
    }
}

// 创建全局关卡管理器实例
window.levelManager = new LevelManager();
