/* 魂斗罗游戏样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    color: white;
    overflow: hidden;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

#gameContainer {
    position: relative;
    width: 800px;
    height: 600px;
    border: 2px solid #00ff00;
    border-radius: 10px;
    overflow: hidden;
    background: #000;
}

/* 菜单样式 */
.menu {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.menu h1 {
    font-size: 3em;
    color: #ff6b35;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 30px;
    font-weight: bold;
}

.menu h2 {
    font-size: 2em;
    color: #00ff00;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 20px;
}

.menu-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-button {
    padding: 12px 30px;
    font-size: 1.2em;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.menu-button:hover {
    background: linear-gradient(45deg, #f7931e, #ff6b35);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 107, 53, 0.4);
}

.menu-button:active {
    transform: translateY(0);
}

/* 游戏界面样式 */
#gameScreen {
    position: relative;
    width: 100%;
    height: 100%;
}

#gameCanvas {
    display: block;
    background: #000;
}

#gameUI {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
}

#playerStats {
    display: flex;
    gap: 20px;
    font-size: 1.1em;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

#playerStats > div {
    background: rgba(0, 0, 0, 0.7);
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid #00ff00;
}

#lives {
    color: #ff4444;
}

#score {
    color: #ffff44;
}

#weapon {
    color: #44ff44;
}

/* 说明界面样式 */
.instructions-content {
    max-width: 600px;
    text-align: left;
    margin-bottom: 20px;
}

.instructions-content p {
    margin-bottom: 15px;
    font-size: 1.1em;
}

.instructions-content ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.instructions-content li {
    margin-bottom: 8px;
    font-size: 1em;
}

.instructions-content strong {
    color: #00ff00;
}

/* 隐藏元素 */
.hidden {
    display: none !important;
}

/* 最终得分显示 */
#finalScore {
    font-size: 1.5em;
    color: #ffff44;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* 响应式设计 */
@media (max-width: 850px) {
    #gameContainer {
        width: 95vw;
        height: 71.25vw; /* 保持4:3比例 */
        max-height: 95vh;
    }
    
    #gameCanvas {
        width: 100%;
        height: 100%;
    }
    
    .menu h1 {
        font-size: 2.5em;
    }
    
    .menu-button {
        font-size: 1em;
        padding: 10px 25px;
    }
}

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.menu h1 {
    animation: pulse 2s infinite;
}

/* 加载动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.menu {
    animation: fadeIn 0.5s ease-in;
}
