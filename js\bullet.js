// 子弹系统

class Bullet {
    constructor() {
        this.reset();
    }

    reset() {
        this.x = 0;
        this.y = 0;
        this.vx = 0;
        this.vy = 0;
        this.width = 4;
        this.height = 8;
        this.damage = 1;
        this.speed = 300;
        this.angle = 0;
        this.color = '#ffff00';
        this.active = false;
        this.owner = null; // 'player' 或 'enemy'
        this.type = 'normal';
        this.piercing = false;
        this.hitCount = 0;
        this.maxHits = 1;
        this.trail = [];
        this.collisionType = 'rect';
    }

    init(x, y, angle, config = {}) {
        this.x = x;
        this.y = y;
        this.angle = angle;
        this.vx = Math.cos(angle) * (config.speed || this.speed);
        this.vy = Math.sin(angle) * (config.speed || this.speed);
        
        this.damage = config.damage || 1;
        this.color = config.color || '#ffff00';
        this.owner = config.owner || 'player';
        this.type = config.type || 'normal';
        this.piercing = config.piercing || false;
        this.maxHits = config.maxHits || 1;
        this.width = config.width || 4;
        this.height = config.height || 8;
        
        this.active = true;
        this.hitCount = 0;
        this.trail = [];
    }

    update(deltaTime) {
        if (!this.active) return;

        // 保存轨迹
        this.trail.push({ x: this.x, y: this.y });
        if (this.trail.length > 5) {
            this.trail.shift();
        }

        // 更新位置
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;

        // 检查边界
        if (this.x < -50 || this.x > 850 || this.y < -50 || this.y > 650) {
            this.active = false;
        }

        // 特殊子弹类型的更新逻辑
        this.updateSpecialBehavior(deltaTime);
    }

    updateSpecialBehavior(deltaTime) {
        switch (this.type) {
            case 'homing':
                this.updateHoming(deltaTime);
                break;
            case 'spread':
                // 扩散子弹可能会分裂
                break;
            case 'laser':
                // 激光子弹持续时间较短
                break;
        }
    }

    updateHoming(deltaTime) {
        // 简单的追踪逻辑（需要目标系统）
        // 这里暂时省略，可以后续添加
    }

    render(ctx) {
        if (!this.active) return;

        ctx.save();

        // 渲染轨迹
        if (this.trail.length > 1) {
            ctx.strokeStyle = this.color;
            ctx.globalAlpha = 0.3;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(this.trail[0].x, this.trail[0].y);
            for (let i = 1; i < this.trail.length; i++) {
                ctx.lineTo(this.trail[i].x, this.trail[i].y);
            }
            ctx.stroke();
        }

        // 渲染子弹本体
        ctx.globalAlpha = 1;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.angle);

        switch (this.type) {
            case 'normal':
                this.renderNormal(ctx);
                break;
            case 'laser':
                this.renderLaser(ctx);
                break;
            case 'rocket':
                this.renderRocket(ctx);
                break;
            case 'spread':
                this.renderSpread(ctx);
                break;
            default:
                this.renderNormal(ctx);
        }

        ctx.restore();
    }

    renderNormal(ctx) {
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        
        // 添加发光效果
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 5;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
    }

    renderLaser(ctx) {
        ctx.strokeStyle = this.color;
        ctx.lineWidth = this.width;
        ctx.lineCap = 'round';
        ctx.beginPath();
        ctx.moveTo(-this.height/2, 0);
        ctx.lineTo(this.height/2, 0);
        ctx.stroke();
        
        // 内核
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = this.width * 0.3;
        ctx.stroke();
    }

    renderRocket(ctx) {
        // 火箭弹体
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        
        // 火箭尾焰
        ctx.fillStyle = '#ff4400';
        ctx.beginPath();
        ctx.moveTo(-this.width/2, -this.height/4);
        ctx.lineTo(-this.width, 0);
        ctx.lineTo(-this.width/2, this.height/4);
        ctx.closePath();
        ctx.fill();
    }

    renderSpread(ctx) {
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width/2, 0, Math.PI * 2);
        ctx.fill();
        
        // 发光效果
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 8;
        ctx.fill();
    }

    onHit(target) {
        this.hitCount++;
        
        // 创建击中特效
        particleSystem.createBulletHit(this.x, this.y, this.angle + Math.PI);
        
        // 如果不是穿透子弹或达到最大击中次数，则销毁
        if (!this.piercing || this.hitCount >= this.maxHits) {
            this.active = false;
        }
        
        return this.damage;
    }

    getBounds() {
        return {
            left: this.x - this.width/2,
            right: this.x + this.width/2,
            top: this.y - this.height/2,
            bottom: this.y + this.height/2
        };
    }
}

class BulletManager {
    constructor() {
        this.bullets = [];
        this.pool = new ObjectPool(
            () => new Bullet(),
            (bullet) => bullet.reset(),
            50
        );
    }

    createBullet(x, y, angle, config = {}) {
        const bullet = this.pool.get();
        bullet.init(x, y, angle, config);
        this.bullets.push(bullet);
        return bullet;
    }

    // 创建扩散子弹
    createSpreadBullets(x, y, baseAngle, count = 3, spread = Math.PI/6, config = {}) {
        const bullets = [];
        const angleStep = spread / (count - 1);
        const startAngle = baseAngle - spread/2;
        
        for (let i = 0; i < count; i++) {
            const angle = startAngle + angleStep * i;
            const bullet = this.createBullet(x, y, angle, {
                ...config,
                type: 'spread'
            });
            bullets.push(bullet);
        }
        
        return bullets;
    }

    // 创建环形子弹
    createCircularBullets(x, y, count = 8, config = {}) {
        const bullets = [];
        const angleStep = (Math.PI * 2) / count;
        
        for (let i = 0; i < count; i++) {
            const angle = angleStep * i;
            const bullet = this.createBullet(x, y, angle, config);
            bullets.push(bullet);
        }
        
        return bullets;
    }

    update(deltaTime) {
        // 更新所有子弹
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            bullet.update(deltaTime);
            
            // 移除非活跃子弹
            if (!bullet.active) {
                this.pool.release(bullet);
                this.bullets.splice(i, 1);
            }
        }
    }

    render(ctx) {
        // 渲染所有子弹
        for (const bullet of this.bullets) {
            bullet.render(ctx);
        }
    }

    getPlayerBullets() {
        return this.bullets.filter(bullet => bullet.owner === 'player' && bullet.active);
    }

    getEnemyBullets() {
        return this.bullets.filter(bullet => bullet.owner === 'enemy' && bullet.active);
    }

    clear() {
        // 清除所有子弹
        for (const bullet of this.bullets) {
            this.pool.release(bullet);
        }
        this.bullets.length = 0;
    }

    getBulletCount() {
        return this.bullets.length;
    }
}

// 创建全局子弹管理器实例
window.bulletManager = new BulletManager();
