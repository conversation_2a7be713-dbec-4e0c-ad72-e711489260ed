// 输入处理系统

class InputManager {
    constructor() {
        this.keys = {};
        this.keysPressed = {};
        this.keysReleased = {};
        this.mouse = {
            x: 0,
            y: 0,
            buttons: {},
            buttonsPressed: {},
            buttonsReleased: {}
        };
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (!this.keys[e.code]) {
                this.keysPressed[e.code] = true;
            }
            this.keys[e.code] = true;
            
            // 阻止某些默认行为
            if (['Space', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.code)) {
                e.preventDefault();
            }
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
            this.keysReleased[e.code] = true;
        });

        // 鼠标事件
        document.addEventListener('mousemove', (e) => {
            const canvas = document.getElementById('gameCanvas');
            const rect = canvas.getBoundingClientRect();
            this.mouse.x = (e.clientX - rect.left) * (canvas.width / rect.width);
            this.mouse.y = (e.clientY - rect.top) * (canvas.height / rect.height);
        });

        document.addEventListener('mousedown', (e) => {
            if (!this.mouse.buttons[e.button]) {
                this.mouse.buttonsPressed[e.button] = true;
            }
            this.mouse.buttons[e.button] = true;
            e.preventDefault();
        });

        document.addEventListener('mouseup', (e) => {
            this.mouse.buttons[e.button] = false;
            this.mouse.buttonsReleased[e.button] = true;
        });

        // 防止右键菜单
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // 失去焦点时清除所有按键状态
        window.addEventListener('blur', () => {
            this.clearAll();
        });
    }

    // 检查按键是否被按住
    isKeyDown(keyCode) {
        return !!this.keys[keyCode];
    }

    // 检查按键是否刚被按下
    isKeyPressed(keyCode) {
        return !!this.keysPressed[keyCode];
    }

    // 检查按键是否刚被释放
    isKeyReleased(keyCode) {
        return !!this.keysReleased[keyCode];
    }

    // 检查鼠标按钮是否被按住
    isMouseDown(button = 0) {
        return !!this.mouse.buttons[button];
    }

    // 检查鼠标按钮是否刚被按下
    isMousePressed(button = 0) {
        return !!this.mouse.buttonsPressed[button];
    }

    // 检查鼠标按钮是否刚被释放
    isMouseReleased(button = 0) {
        return !!this.mouse.buttonsReleased[button];
    }

    // 获取鼠标位置
    getMousePosition() {
        return { x: this.mouse.x, y: this.mouse.y };
    }

    // 清除单帧状态
    update() {
        this.keysPressed = {};
        this.keysReleased = {};
        this.mouse.buttonsPressed = {};
        this.mouse.buttonsReleased = {};
    }

    // 清除所有状态
    clearAll() {
        this.keys = {};
        this.keysPressed = {};
        this.keysReleased = {};
        this.mouse.buttons = {};
        this.mouse.buttonsPressed = {};
        this.mouse.buttonsReleased = {};
    }

    // 游戏控制映射
    getMovementInput() {
        const input = { x: 0, y: 0 };
        
        // WASD 控制
        if (this.isKeyDown('KeyW') || this.isKeyDown('ArrowUp')) {
            input.y = -1;
        }
        if (this.isKeyDown('KeyS') || this.isKeyDown('ArrowDown')) {
            input.y = 1;
        }
        if (this.isKeyDown('KeyA') || this.isKeyDown('ArrowLeft')) {
            input.x = -1;
        }
        if (this.isKeyDown('KeyD') || this.isKeyDown('ArrowRight')) {
            input.x = 1;
        }

        // 标准化对角线移动
        if (input.x !== 0 && input.y !== 0) {
            const length = Math.sqrt(input.x * input.x + input.y * input.y);
            input.x /= length;
            input.y /= length;
        }

        return input;
    }

    // 射击输入
    isShootPressed() {
        return this.isKeyPressed('Space') || this.isMousePressed(0);
    }

    // 射击按住
    isShootHeld() {
        return this.isKeyDown('Space') || this.isMouseDown(0);
    }

    // 暂停输入
    isPausePressed() {
        return this.isKeyPressed('KeyP') || this.isKeyPressed('Escape');
    }

    // 重启输入
    isRestartPressed() {
        return this.isKeyPressed('KeyR');
    }

    // 确认输入
    isConfirmPressed() {
        return this.isKeyPressed('Enter') || this.isKeyPressed('Space') || this.isMousePressed(0);
    }

    // 取消输入
    isCancelPressed() {
        return this.isKeyPressed('Escape') || this.isKeyPressed('Backspace');
    }
}

// 游戏手柄支持（可选）
class GamepadManager {
    constructor() {
        this.gamepads = {};
        this.deadzone = 0.1;
        
        window.addEventListener('gamepadconnected', (e) => {
            console.log('手柄已连接:', e.gamepad.id);
            this.gamepads[e.gamepad.index] = e.gamepad;
        });

        window.addEventListener('gamepaddisconnected', (e) => {
            console.log('手柄已断开:', e.gamepad.id);
            delete this.gamepads[e.gamepad.index];
        });
    }

    update() {
        // 更新手柄状态
        const gamepads = navigator.getGamepads();
        for (let i = 0; i < gamepads.length; i++) {
            if (gamepads[i]) {
                this.gamepads[i] = gamepads[i];
            }
        }
    }

    getMovementInput(gamepadIndex = 0) {
        const gamepad = this.gamepads[gamepadIndex];
        if (!gamepad) return { x: 0, y: 0 };

        let x = gamepad.axes[0];
        let y = gamepad.axes[1];

        // 应用死区
        if (Math.abs(x) < this.deadzone) x = 0;
        if (Math.abs(y) < this.deadzone) y = 0;

        return { x, y };
    }

    isButtonPressed(buttonIndex, gamepadIndex = 0) {
        const gamepad = this.gamepads[gamepadIndex];
        if (!gamepad) return false;
        return gamepad.buttons[buttonIndex] && gamepad.buttons[buttonIndex].pressed;
    }

    isShootPressed(gamepadIndex = 0) {
        return this.isButtonPressed(0, gamepadIndex) || // A按钮
               this.isButtonPressed(7, gamepadIndex);   // RT按钮
    }
}

// 创建全局输入管理器实例
window.inputManager = new InputManager();
window.gamepadManager = new GamepadManager();
