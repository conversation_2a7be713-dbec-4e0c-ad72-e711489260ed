// 碰撞检测系统

class CollisionSystem {
    constructor() {
        this.collisionGroups = new Map();
    }

    // 添加碰撞组
    addGroup(name) {
        if (!this.collisionGroups.has(name)) {
            this.collisionGroups.set(name, []);
        }
    }

    // 添加对象到碰撞组
    addToGroup(groupName, object) {
        if (!this.collisionGroups.has(groupName)) {
            this.addGroup(groupName);
        }
        this.collisionGroups.get(groupName).push(object);
    }

    // 从碰撞组移除对象
    removeFromGroup(groupName, object) {
        if (this.collisionGroups.has(groupName)) {
            const group = this.collisionGroups.get(groupName);
            const index = group.indexOf(object);
            if (index > -1) {
                group.splice(index, 1);
            }
        }
    }

    // 清空碰撞组
    clearGroup(groupName) {
        if (this.collisionGroups.has(groupName)) {
            this.collisionGroups.get(groupName).length = 0;
        }
    }

    // 清空所有碰撞组
    clearAll() {
        this.collisionGroups.clear();
    }

    // 检测两个组之间的碰撞
    checkCollisions(group1Name, group2Name, callback) {
        const group1 = this.collisionGroups.get(group1Name) || [];
        const group2 = this.collisionGroups.get(group2Name) || [];

        for (let i = 0; i < group1.length; i++) {
            const obj1 = group1[i];
            if (!obj1.active) continue;

            for (let j = 0; j < group2.length; j++) {
                const obj2 = group2[j];
                if (!obj2.active) continue;

                if (this.checkCollision(obj1, obj2)) {
                    callback(obj1, obj2);
                }
            }
        }
    }

    // 检测组内碰撞
    checkGroupCollisions(groupName, callback) {
        const group = this.collisionGroups.get(groupName) || [];

        for (let i = 0; i < group.length; i++) {
            const obj1 = group[i];
            if (!obj1.active) continue;

            for (let j = i + 1; j < group.length; j++) {
                const obj2 = group[j];
                if (!obj2.active) continue;

                if (this.checkCollision(obj1, obj2)) {
                    callback(obj1, obj2);
                }
            }
        }
    }

    // 基础碰撞检测
    checkCollision(obj1, obj2) {
        // 根据对象的碰撞类型选择检测方法
        const type1 = obj1.collisionType || 'rect';
        const type2 = obj2.collisionType || 'rect';

        if (type1 === 'rect' && type2 === 'rect') {
            return this.rectToRect(obj1, obj2);
        } else if (type1 === 'circle' && type2 === 'circle') {
            return this.circleToCircle(obj1, obj2);
        } else if ((type1 === 'rect' && type2 === 'circle') || (type1 === 'circle' && type2 === 'rect')) {
            return this.rectToCircle(
                type1 === 'rect' ? obj1 : obj2,
                type1 === 'circle' ? obj1 : obj2
            );
        }

        return false;
    }

    // 矩形与矩形碰撞检测
    rectToRect(rect1, rect2) {
        const r1 = this.getBounds(rect1);
        const r2 = this.getBounds(rect2);

        return r1.left < r2.right &&
               r1.right > r2.left &&
               r1.top < r2.bottom &&
               r1.bottom > r2.top;
    }

    // 圆形与圆形碰撞检测
    circleToCircle(circle1, circle2) {
        const dx = circle1.x - circle2.x;
        const dy = circle1.y - circle2.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const radiusSum = (circle1.radius || circle1.width / 2) + (circle2.radius || circle2.width / 2);

        return distance < radiusSum;
    }

    // 矩形与圆形碰撞检测
    rectToCircle(rect, circle) {
        const rectBounds = this.getBounds(rect);
        const circleRadius = circle.radius || circle.width / 2;

        // 找到矩形上最接近圆心的点
        const closestX = MathUtils.clamp(circle.x, rectBounds.left, rectBounds.right);
        const closestY = MathUtils.clamp(circle.y, rectBounds.top, rectBounds.bottom);

        // 计算距离
        const dx = circle.x - closestX;
        const dy = circle.y - closestY;
        const distance = Math.sqrt(dx * dx + dy * dy);

        return distance < circleRadius;
    }

    // 获取对象边界
    getBounds(obj) {
        const halfWidth = (obj.width || 0) / 2;
        const halfHeight = (obj.height || 0) / 2;

        return {
            left: obj.x - halfWidth,
            right: obj.x + halfWidth,
            top: obj.y - halfHeight,
            bottom: obj.y + halfHeight
        };
    }

    // 点与矩形碰撞检测
    pointToRect(point, rect) {
        const bounds = this.getBounds(rect);
        return point.x >= bounds.left &&
               point.x <= bounds.right &&
               point.y >= bounds.top &&
               point.y <= bounds.bottom;
    }

    // 点与圆形碰撞检测
    pointToCircle(point, circle) {
        const dx = point.x - circle.x;
        const dy = point.y - circle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const radius = circle.radius || circle.width / 2;

        return distance <= radius;
    }

    // 线段与矩形碰撞检测
    lineToRect(line, rect) {
        const bounds = this.getBounds(rect);
        
        // 检查线段端点是否在矩形内
        if (this.pointToRect(line.start, rect) || this.pointToRect(line.end, rect)) {
            return true;
        }

        // 检查线段是否与矩形边相交
        return this.lineIntersection(line.start, line.end, {x: bounds.left, y: bounds.top}, {x: bounds.right, y: bounds.top}) ||
               this.lineIntersection(line.start, line.end, {x: bounds.right, y: bounds.top}, {x: bounds.right, y: bounds.bottom}) ||
               this.lineIntersection(line.start, line.end, {x: bounds.right, y: bounds.bottom}, {x: bounds.left, y: bounds.bottom}) ||
               this.lineIntersection(line.start, line.end, {x: bounds.left, y: bounds.bottom}, {x: bounds.left, y: bounds.top});
    }

    // 线段相交检测
    lineIntersection(p1, p2, p3, p4) {
        const denom = (p1.x - p2.x) * (p3.y - p4.y) - (p1.y - p2.y) * (p3.x - p4.x);
        if (denom === 0) return false;

        const t = ((p1.x - p3.x) * (p3.y - p4.y) - (p1.y - p3.y) * (p3.x - p4.x)) / denom;
        const u = -((p1.x - p2.x) * (p1.y - p3.y) - (p1.y - p2.y) * (p1.x - p3.x)) / denom;

        return t >= 0 && t <= 1 && u >= 0 && u <= 1;
    }

    // 获取碰撞信息
    getCollisionInfo(obj1, obj2) {
        const bounds1 = this.getBounds(obj1);
        const bounds2 = this.getBounds(obj2);

        const overlapX = Math.min(bounds1.right, bounds2.right) - Math.max(bounds1.left, bounds2.left);
        const overlapY = Math.min(bounds1.bottom, bounds2.bottom) - Math.max(bounds1.top, bounds2.top);

        let normal = { x: 0, y: 0 };
        let penetration = 0;

        if (overlapX < overlapY) {
            penetration = overlapX;
            normal.x = bounds1.left < bounds2.left ? -1 : 1;
        } else {
            penetration = overlapY;
            normal.y = bounds1.top < bounds2.top ? -1 : 1;
        }

        return { normal, penetration };
    }
}

// 创建全局碰撞系统实例
window.collisionSystem = new CollisionSystem();
