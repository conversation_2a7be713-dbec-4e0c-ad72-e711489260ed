// 工具函数集合

// 数学工具函数
const MathUtils = {
    // 计算两点之间的距离
    distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    },

    // 计算两点之间的角度
    angle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    },

    // 角度转弧度
    toRadians(degrees) {
        return degrees * Math.PI / 180;
    },

    // 弧度转角度
    toDegrees(radians) {
        return radians * 180 / Math.PI;
    },

    // 限制数值在指定范围内
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },

    // 线性插值
    lerp(start, end, factor) {
        return start + (end - start) * factor;
    },

    // 随机整数
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },

    // 随机浮点数
    randomFloat(min, max) {
        return Math.random() * (max - min) + min;
    },

    // 标准化向量
    normalize(x, y) {
        const length = Math.sqrt(x * x + y * y);
        if (length === 0) return { x: 0, y: 0 };
        return { x: x / length, y: y / length };
    }
};
        return { x: x / length, y: y / length };
    }
};

// 颜色工具
const ColorUtils = {
    // RGB转十六进制
    rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    },

    // 十六进制转RGB
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    },

    // 颜色插值
    lerpColor(color1, color2, factor) {
        const c1 = this.hexToRgb(color1);
        const c2 = this.hexToRgb(color2);
        if (!c1 || !c2) return color1;
        
        const r = Math.round(MathUtils.lerp(c1.r, c2.r, factor));
        const g = Math.round(MathUtils.lerp(c1.g, c2.g, factor));
        const b = Math.round(MathUtils.lerp(c1.b, c2.b, factor));
        
        return this.rgbToHex(r, g, b);
    }
};

// 时间工具
const TimeUtils = {
    // 获取当前时间戳
    now() {
        return Date.now();
    },

    // 格式化时间
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
};

// 数组工具
const ArrayUtils = {
    // 随机选择数组元素
    randomChoice(array) {
        return array[Math.floor(Math.random() * array.length)];
    },

    // 打乱数组
    shuffle(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    },

    // 移除数组元素
    remove(array, item) {
        const index = array.indexOf(item);
        if (index > -1) {
            array.splice(index, 1);
        }
        return array;
    }
};

// 游戏对象池
class ObjectPool {
    constructor(createFn, resetFn, initialSize = 10) {
        this.createFn = createFn;
        this.resetFn = resetFn;
        this.pool = [];
        this.active = [];
        
        // 预创建对象
        for (let i = 0; i < initialSize; i++) {
            this.pool.push(this.createFn());
        }
    }

    get() {
        let obj;
        if (this.pool.length > 0) {
            obj = this.pool.pop();
        } else {
            obj = this.createFn();
        }
        this.active.push(obj);
        return obj;
    }

    release(obj) {
        const index = this.active.indexOf(obj);
        if (index > -1) {
            this.active.splice(index, 1);
            this.resetFn(obj);
            this.pool.push(obj);
        }
    }

    releaseAll() {
        while (this.active.length > 0) {
            this.release(this.active[0]);
        }
    }

    getActiveCount() {
        return this.active.length;
    }
}

// 简单的事件系统
class EventEmitter {
    constructor() {
        this.events = {};
    }

    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }

    off(event, callback) {
        if (!this.events[event]) return;
        const index = this.events[event].indexOf(callback);
        if (index > -1) {
            this.events[event].splice(index, 1);
        }
    }

    emit(event, ...args) {
        if (!this.events[event]) return;
        this.events[event].forEach(callback => callback(...args));
    }
}

// 简单的状态机
class StateMachine {
    constructor(initialState) {
        this.currentState = initialState;
        this.states = {};
    }

    addState(name, state) {
        this.states[name] = state;
    }

    changeState(newState) {
        if (this.states[this.currentState] && this.states[this.currentState].exit) {
            this.states[this.currentState].exit();
        }
        
        this.currentState = newState;
        
        if (this.states[this.currentState] && this.states[this.currentState].enter) {
            this.states[this.currentState].enter();
        }
    }

    update(deltaTime) {
        if (this.states[this.currentState] && this.states[this.currentState].update) {
            this.states[this.currentState].update(deltaTime);
        }
    }
}

// 导出所有工具
window.MathUtils = MathUtils;
window.ColorUtils = ColorUtils;
window.TimeUtils = TimeUtils;
window.ArrayUtils = ArrayUtils;
window.ObjectPool = ObjectPool;
window.EventEmitter = EventEmitter;
window.StateMachine = StateMachine;
