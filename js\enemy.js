// 敌人系统

class Enemy {
    constructor(x, y, type = 'soldier') {
        this.x = x;
        this.y = y;
        this.width = 20;
        this.height = 24;
        this.vx = 0;
        this.vy = 0;
        this.type = type;
        this.health = 1;
        this.maxHealth = 1;
        this.damage = 1;
        this.speed = 50;
        this.active = true;
        this.facing = -1; // 默认面向左（玩家方向）
        
        // AI状态
        this.aiState = 'patrol';
        this.aiTimer = 0;
        this.targetX = x;
        this.targetY = y;
        this.detectionRange = 150;
        this.attackRange = 100;
        this.lastAttackTime = 0;
        this.attackCooldown = 1.0;
        
        // 动画
        this.animationTime = 0;
        this.frameIndex = 0;
        
        // 碰撞
        this.collisionType = 'rect';
        
        // 分数值
        this.scoreValue = 100;
        
        // 掉落物品
        this.dropChance = 0.3;
        this.dropType = 'powerup';
        
        this.setupEnemyStats();
    }

    setupEnemyStats() {
        switch (this.type) {
            case 'soldier':
                this.health = 1;
                this.maxHealth = 1;
                this.speed = 60;
                this.damage = 1;
                this.scoreValue = 100;
                this.attackCooldown = 1.5;
                this.width = 20;
                this.height = 24;
                break;
                
            case 'heavy':
                this.health = 3;
                this.maxHealth = 3;
                this.speed = 30;
                this.damage = 2;
                this.scoreValue = 300;
                this.attackCooldown = 2.0;
                this.width = 28;
                this.height = 32;
                break;
                
            case 'fast':
                this.health = 1;
                this.maxHealth = 1;
                this.speed = 120;
                this.damage = 1;
                this.scoreValue = 150;
                this.attackCooldown = 0.8;
                this.width = 16;
                this.height = 20;
                break;
                
            case 'sniper':
                this.health = 2;
                this.maxHealth = 2;
                this.speed = 20;
                this.damage = 2;
                this.scoreValue = 250;
                this.attackCooldown = 3.0;
                this.attackRange = 300;
                this.detectionRange = 350;
                this.width = 18;
                this.height = 22;
                break;
                
            case 'boss':
                this.health = 20;
                this.maxHealth = 20;
                this.speed = 40;
                this.damage = 3;
                this.scoreValue = 1000;
                this.attackCooldown = 1.0;
                this.width = 40;
                this.height = 48;
                this.dropChance = 1.0;
                break;
        }
    }

    update(deltaTime) {
        if (!this.active) return;

        this.animationTime += deltaTime;
        this.aiTimer += deltaTime;
        
        // 更新AI
        this.updateAI(deltaTime);
        
        // 更新物理
        this.updatePhysics(deltaTime);
        
        // 边界检查
        this.checkBounds();
    }

    updateAI(deltaTime) {
        const playerDistance = MathUtils.distance(this.x, this.y, player.x, player.y);
        const currentTime = Date.now() / 1000;
        
        // 状态机
        switch (this.aiState) {
            case 'patrol':
                this.patrol(deltaTime);
                
                // 检测玩家
                if (playerDistance <= this.detectionRange) {
                    this.aiState = 'chase';
                    this.aiTimer = 0;
                }
                break;
                
            case 'chase':
                this.chase(deltaTime);
                
                // 检查攻击范围
                if (playerDistance <= this.attackRange) {
                    this.aiState = 'attack';
                    this.aiTimer = 0;
                }
                
                // 失去目标
                if (playerDistance > this.detectionRange * 1.5) {
                    this.aiState = 'patrol';
                    this.aiTimer = 0;
                }
                break;
                
            case 'attack':
                this.attack(currentTime);
                
                // 攻击后返回追击
                if (this.aiTimer > 0.5) {
                    this.aiState = 'chase';
                    this.aiTimer = 0;
                }
                break;
        }
    }

    patrol(deltaTime) {
        // 简单的巡逻逻辑
        if (this.aiTimer > 2.0) {
            this.targetX = this.x + MathUtils.randomFloat(-100, 100);
            this.aiTimer = 0;
        }
        
        this.moveTowards(this.targetX, this.y, deltaTime);
    }

    chase(deltaTime) {
        this.moveTowards(player.x, player.y, deltaTime);
    }

    attack(currentTime) {
        if (currentTime - this.lastAttackTime >= this.attackCooldown) {
            this.performAttack();
            this.lastAttackTime = currentTime;
        }
    }

    moveTowards(targetX, targetY, deltaTime) {
        const dx = targetX - this.x;
        const dy = targetY - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 5) {
            const normalizedX = dx / distance;
            const normalizedY = dy / distance;
            
            this.vx = normalizedX * this.speed;
            this.vy = normalizedY * this.speed;
            
            // 更新朝向
            this.facing = normalizedX > 0 ? 1 : -1;
        } else {
            this.vx = 0;
            this.vy = 0;
        }
    }

    performAttack() {
        const angle = MathUtils.angle(this.x, this.y, player.x, player.y);
        
        switch (this.type) {
            case 'soldier':
            case 'fast':
                // 单发射击
                bulletManager.createBullet(this.x, this.y, angle, {
                    damage: this.damage,
                    speed: 200,
                    color: '#ff4444',
                    owner: 'enemy',
                    type: 'normal'
                });
                break;
                
            case 'heavy':
                // 三连发
                for (let i = 0; i < 3; i++) {
                    setTimeout(() => {
                        if (this.active) {
                            bulletManager.createBullet(this.x, this.y, angle, {
                                damage: this.damage,
                                speed: 180,
                                color: '#ff6600',
                                owner: 'enemy',
                                type: 'normal'
                            });
                        }
                    }, i * 100);
                }
                break;
                
            case 'sniper':
                // 高速精确射击
                bulletManager.createBullet(this.x, this.y, angle, {
                    damage: this.damage,
                    speed: 400,
                    color: '#ff0088',
                    owner: 'enemy',
                    type: 'laser'
                });
                break;
                
            case 'boss':
                // Boss特殊攻击模式
                this.bossAttack(angle);
                break;
        }
        
        // 创建枪口火焰
        particleSystem.createSparks(this.x, this.y, 3, {
            colors: ['#ff4444', '#ff8844'],
            minSpeed: 50,
            maxSpeed: 80,
            life: 0.2
        });
    }

    bossAttack(baseAngle) {
        // Boss的多种攻击模式
        const attackPattern = Math.floor(Math.random() * 3);
        
        switch (attackPattern) {
            case 0:
                // 扇形射击
                bulletManager.createSpreadBullets(this.x, this.y, baseAngle, 5, Math.PI/3, {
                    damage: this.damage,
                    speed: 150,
                    color: '#ff0000',
                    owner: 'enemy'
                });
                break;
                
            case 1:
                // 环形射击
                bulletManager.createCircularBullets(this.x, this.y, 8, {
                    damage: this.damage,
                    speed: 120,
                    color: '#ff4400',
                    owner: 'enemy'
                });
                break;
                
            case 2:
                // 追踪弹
                bulletManager.createBullet(this.x, this.y, baseAngle, {
                    damage: this.damage * 2,
                    speed: 100,
                    color: '#ff00ff',
                    owner: 'enemy',
                    type: 'homing'
                });
                break;
        }
    }

    updatePhysics(deltaTime) {
        // 简单的物理更新
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;
        
        // 简单的地面检测
        const groundY = 550;
        if (this.y + this.height/2 > groundY) {
            this.y = groundY - this.height/2;
        }
    }

    checkBounds() {
        // 保持在屏幕范围内
        if (this.x < 0) this.x = 0;
        if (this.x > 800) this.x = 800;
        if (this.y < 0) this.y = 0;
    }

    takeDamage(damage) {
        this.health -= damage;
        
        // 创建受伤特效
        particleSystem.createBlood(this.x, this.y, Math.PI, {
            colors: ['#cc0000', '#990000'],
            count: 6
        });
        
        if (this.health <= 0) {
            this.die();
        }
        
        return this.health <= 0;
    }

    die() {
        this.active = false;
        
        // 创建死亡爆炸
        particleSystem.createExplosion(this.x, this.y, 15, {
            colors: ['#ff4444', '#ff8844', '#ffff44'],
            minSpeed: 80,
            maxSpeed: 150
        });
        
        // 给玩家加分
        player.addScore(this.scoreValue);
        
        // 掉落道具
        if (Math.random() < this.dropChance) {
            this.dropPowerup();
        }
    }

    dropPowerup() {
        // 这里可以创建道具掉落
        // 暂时用粒子效果表示
        particleSystem.createSparks(this.x, this.y, 8, {
            colors: ['#00ff00', '#00ffff', '#ffff00'],
            minSpeed: 60,
            maxSpeed: 100,
            life: 2.0,
            gravity: 100
        });
    }

    render(ctx) {
        if (!this.active) return;

        ctx.save();
        ctx.translate(this.x, this.y);
        ctx.scale(this.facing, 1);
        
        // 渲染敌人
        this.renderSprite(ctx);
        
        // 渲染血条
        this.renderHealthBar(ctx);
        
        ctx.restore();
    }

    renderSprite(ctx) {
        // 根据类型渲染不同的敌人
        switch (this.type) {
            case 'soldier':
                ctx.fillStyle = '#ff4444';
                break;
            case 'heavy':
                ctx.fillStyle = '#cc2222';
                break;
            case 'fast':
                ctx.fillStyle = '#ff6666';
                break;
            case 'sniper':
                ctx.fillStyle = '#884444';
                break;
            case 'boss':
                ctx.fillStyle = '#660000';
                break;
        }
        
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        
        // 头部
        ctx.fillStyle = '#ffddaa';
        ctx.fillRect(-this.width/3, -this.height/2, this.width*2/3, this.height/4);
        
        // 武器
        ctx.fillStyle = '#333333';
        ctx.fillRect(this.width/2 - 2, -2, 6, 4);
    }

    renderHealthBar(ctx) {
        if (this.health >= this.maxHealth) return;
        
        const barWidth = this.width;
        const barHeight = 4;
        const healthRatio = this.health / this.maxHealth;
        
        // 背景
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(-barWidth/2, -this.height/2 - 8, barWidth, barHeight);
        
        // 血量
        ctx.fillStyle = '#00ff00';
        ctx.fillRect(-barWidth/2, -this.height/2 - 8, barWidth * healthRatio, barHeight);
    }

    getBounds() {
        return {
            left: this.x - this.width/2,
            right: this.x + this.width/2,
            top: this.y - this.height/2,
            bottom: this.y + this.height/2
        };
    }
}

class EnemyManager {
    constructor() {
        this.enemies = [];
        this.spawnTimer = 0;
        this.spawnInterval = 3.0;
        this.maxEnemies = 10;
        this.waveNumber = 1;
        this.enemiesKilled = 0;
    }

    update(deltaTime) {
        // 更新所有敌人
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            enemy.update(deltaTime);
            
            // 移除死亡的敌人
            if (!enemy.active) {
                this.enemies.splice(i, 1);
                this.enemiesKilled++;
            }
        }
        
        // 生成新敌人
        this.spawnTimer += deltaTime;
        if (this.spawnTimer >= this.spawnInterval && this.enemies.length < this.maxEnemies) {
            this.spawnEnemy();
            this.spawnTimer = 0;
        }
    }

    spawnEnemy() {
        const spawnX = Math.random() > 0.5 ? 850 : -50; // 从左右两侧生成
        const spawnY = 500;
        
        // 根据波数选择敌人类型
        let enemyType = 'soldier';
        const rand = Math.random();
        
        if (this.waveNumber >= 3) {
            if (rand < 0.1) enemyType = 'boss';
            else if (rand < 0.3) enemyType = 'heavy';
            else if (rand < 0.5) enemyType = 'sniper';
            else if (rand < 0.7) enemyType = 'fast';
        } else if (this.waveNumber >= 2) {
            if (rand < 0.2) enemyType = 'heavy';
            else if (rand < 0.4) enemyType = 'fast';
        }
        
        const enemy = new Enemy(spawnX, spawnY, enemyType);
        this.enemies.push(enemy);
    }

    render(ctx) {
        for (const enemy of this.enemies) {
            enemy.render(ctx);
        }
    }

    getEnemies() {
        return this.enemies.filter(enemy => enemy.active);
    }

    clear() {
        this.enemies = [];
    }

    getEnemyCount() {
        return this.enemies.length;
    }
}

// 创建全局敌人管理器实例
window.enemyManager = new EnemyManager();
