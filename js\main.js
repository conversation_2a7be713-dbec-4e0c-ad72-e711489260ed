// 主入口文件 - 初始化和事件绑定

document.addEventListener('DOMContentLoaded', function() {
    console.log('魂斗罗游戏加载中...');
    
    // 等待所有资源加载完成
    initializeGame();
});

function initializeGame() {
    // 绑定菜单事件
    bindMenuEvents();
    
    // 绑定键盘事件
    bindKeyboardEvents();
    
    // 设置画布
    setupCanvas();
    
    // 显示开始菜单
    showStartMenu();
    
    console.log('魂斗罗游戏初始化完成！');
}

function bindMenuEvents() {
    // 开始游戏按钮
    document.getElementById('startButton').addEventListener('click', () => {
        game.startGame();
    });
    
    // 游戏说明按钮
    document.getElementById('instructionsButton').addEventListener('click', () => {
        showInstructions();
    });
    
    // 返回按钮（从说明页面）
    document.getElementById('backButton').addEventListener('click', () => {
        hideInstructions();
    });
    
    // 暂停菜单按钮
    document.getElementById('resumeButton').addEventListener('click', () => {
        game.resumeGame();
    });
    
    document.getElementById('restartButton').addEventListener('click', () => {
        game.restartGame();
    });
    
    document.getElementById('mainMenuButton').addEventListener('click', () => {
        game.returnToMenu();
    });
    
    // 游戏结束菜单按钮
    document.getElementById('playAgainButton').addEventListener('click', () => {
        game.restartGame();
    });
    
    document.getElementById('backToMenuButton').addEventListener('click', () => {
        game.returnToMenu();
    });
}

function bindKeyboardEvents() {
    // 全局键盘事件
    document.addEventListener('keydown', (e) => {
        // 在菜单状态下的特殊处理
        if (game.gameState === 'menu') {
            if (e.code === 'Enter' || e.code === 'Space') {
                game.startGame();
                e.preventDefault();
            }
        }
        
        // 在游戏结束状态下的特殊处理
        if (game.gameState === 'gameOver') {
            if (e.code === 'KeyR') {
                game.restartGame();
                e.preventDefault();
            }
            if (e.code === 'Escape') {
                game.returnToMenu();
                e.preventDefault();
            }
        }
        
        // 在暂停状态下的特殊处理
        if (game.gameState === 'paused') {
            if (e.code === 'KeyP' || e.code === 'Escape') {
                game.resumeGame();
                e.preventDefault();
            }
        }
        
        // 调试功能
        if (e.code === 'F1') {
            game.toggleDebugInfo();
            e.preventDefault();
        }
        
        // 全屏切换
        if (e.code === 'F11') {
            toggleFullscreen();
            e.preventDefault();
        }
    });
}

function setupCanvas() {
    const canvas = document.getElementById('gameCanvas');
    const container = document.getElementById('gameContainer');
    
    // 设置画布大小
    canvas.width = 800;
    canvas.height = 600;
    
    // 禁用右键菜单
    canvas.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });
    
    // 防止画布被选中
    canvas.addEventListener('selectstart', (e) => {
        e.preventDefault();
    });
    
    // 确保画布获得焦点
    canvas.tabIndex = 1;
    canvas.focus();
}

function showStartMenu() {
    document.getElementById('startMenu').classList.remove('hidden');
    document.getElementById('gameScreen').classList.add('hidden');
    document.getElementById('pauseMenu').classList.add('hidden');
    document.getElementById('gameOverMenu').classList.add('hidden');
    document.getElementById('instructions').classList.add('hidden');
}

function showInstructions() {
    document.getElementById('startMenu').classList.add('hidden');
    document.getElementById('instructions').classList.remove('hidden');
}

function hideInstructions() {
    document.getElementById('instructions').classList.add('hidden');
    document.getElementById('startMenu').classList.remove('hidden');
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.log('无法进入全屏模式:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

// 窗口失去焦点时自动暂停游戏
window.addEventListener('blur', () => {
    if (game.gameState === 'playing') {
        game.pauseGame();
    }
});

// 处理窗口大小变化
window.addEventListener('resize', () => {
    // 这里可以添加响应式处理逻辑
    adjustCanvasSize();
});

function adjustCanvasSize() {
    const canvas = document.getElementById('gameCanvas');
    const container = document.getElementById('gameContainer');
    
    // 获取容器大小
    const containerRect = container.getBoundingClientRect();
    
    // 计算缩放比例，保持16:12的宽高比
    const targetRatio = 4 / 3;
    const containerRatio = containerRect.width / containerRect.height;
    
    if (containerRatio > targetRatio) {
        // 容器更宽，以高度为准
        canvas.style.height = '100%';
        canvas.style.width = 'auto';
    } else {
        // 容器更高，以宽度为准
        canvas.style.width = '100%';
        canvas.style.height = 'auto';
    }
}

// 性能监控
let performanceMonitor = {
    lastTime: 0,
    frameCount: 0,
    
    update() {
        this.frameCount++;
        const now = performance.now();
        
        if (now - this.lastTime >= 5000) { // 每5秒检查一次
            const fps = this.frameCount / 5;
            
            if (fps < 30) {
                console.warn('性能警告: FPS过低 (' + fps.toFixed(1) + ')');
                // 可以在这里添加性能优化措施
            }
            
            this.frameCount = 0;
            this.lastTime = now;
        }
    }
};

// 错误处理
window.addEventListener('error', (e) => {
    console.error('游戏错误:', e.error);
    
    // 可以在这里添加错误报告逻辑
    if (game.gameState === 'playing') {
        game.pauseGame();
        alert('游戏遇到错误，已自动暂停。请检查控制台获取详细信息。');
    }
});

// 未捕获的Promise错误
window.addEventListener('unhandledrejection', (e) => {
    console.error('未处理的Promise错误:', e.reason);
});

// 游戏提示系统
class GameTips {
    constructor() {
        this.tips = [
            '使用WASD或方向键移动角色',
            '空格键或鼠标左键射击',
            '按P键暂停游戏',
            '按R键重新开始',
            '收集道具可以获得新武器',
            '红色道具可以恢复生命值',
            '黄色道具可以增加得分',
            '不同武器有不同的特点',
            '注意躲避敌人的攻击',
            '消灭更多敌人获得高分'
        ];
        this.currentTip = 0;
        this.showTips = true;
    }
    
    getRandomTip() {
        return this.tips[Math.floor(Math.random() * this.tips.length)];
    }
    
    getNextTip() {
        const tip = this.tips[this.currentTip];
        this.currentTip = (this.currentTip + 1) % this.tips.length;
        return tip;
    }
}

// 创建游戏提示实例
const gameTips = new GameTips();

// 音频管理器（预留）
class AudioManager {
    constructor() {
        this.sounds = {};
        this.musicVolume = 0.5;
        this.sfxVolume = 0.7;
        this.muted = false;
    }
    
    loadSound(name, url) {
        // 预留音频加载功能
    }
    
    playSound(name, volume = 1) {
        // 预留音频播放功能
    }
    
    playMusic(name, loop = true) {
        // 预留音乐播放功能
    }
    
    stopMusic() {
        // 预留音乐停止功能
    }
    
    setMasterVolume(volume) {
        this.musicVolume = volume;
        this.sfxVolume = volume;
    }
    
    toggleMute() {
        this.muted = !this.muted;
    }
}

// 创建音频管理器实例
const audioManager = new AudioManager();

// 游戏配置
const gameConfig = {
    version: '1.0.0',
    debug: false,
    showFPS: false,
    enableParticles: true,
    enableSound: true,
    autoSave: true
};

// 保存游戏配置
function saveGameConfig() {
    localStorage.setItem('contraGameConfig', JSON.stringify(gameConfig));
}

// 加载游戏配置
function loadGameConfig() {
    const saved = localStorage.getItem('contraGameConfig');
    if (saved) {
        Object.assign(gameConfig, JSON.parse(saved));
    }
}

// 初始化时加载配置
loadGameConfig();

// 导出全局对象供调试使用
window.gameConfig = gameConfig;
window.gameTips = gameTips;
window.audioManager = audioManager;
window.performanceMonitor = performanceMonitor;

console.log('魂斗罗游戏 v' + gameConfig.version + ' 准备就绪！');
console.log('按F1显示调试信息，按F11切换全屏模式');

// 开始性能监控
setInterval(() => {
    performanceMonitor.update();
}, 100);
