// 粒子系统

class Particle {
    constructor() {
        this.reset();
    }

    reset() {
        this.x = 0;
        this.y = 0;
        this.vx = 0;
        this.vy = 0;
        this.life = 1;
        this.maxLife = 1;
        this.size = 1;
        this.color = '#ffffff';
        this.alpha = 1;
        this.gravity = 0;
        this.friction = 1;
        this.active = false;
        this.type = 'default';
    }

    update(deltaTime) {
        if (!this.active) return;

        // 更新位置
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;

        // 应用重力
        this.vy += this.gravity * deltaTime;

        // 应用摩擦力
        this.vx *= this.friction;
        this.vy *= this.friction;

        // 更新生命值
        this.life -= deltaTime;
        if (this.life <= 0) {
            this.active = false;
        }

        // 更新透明度
        this.alpha = this.life / this.maxLife;
    }

    render(ctx) {
        if (!this.active) return;

        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = this.color;

        switch (this.type) {
            case 'circle':
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                break;
            case 'square':
                ctx.fillRect(this.x - this.size/2, this.y - this.size/2, this.size, this.size);
                break;
            case 'spark':
                ctx.strokeStyle = this.color;
                ctx.lineWidth = this.size;
                ctx.beginPath();
                ctx.moveTo(this.x, this.y);
                ctx.lineTo(this.x - this.vx * 0.1, this.y - this.vy * 0.1);
                ctx.stroke();
                break;
            default:
                ctx.fillRect(this.x - this.size/2, this.y - this.size/2, this.size, this.size);
        }

        ctx.restore();
    }
}

class ParticleSystem {
    constructor() {
        this.particles = [];
        this.pool = new ObjectPool(
            () => new Particle(),
            (particle) => particle.reset(),
            100
        );
    }

    // 创建爆炸效果
    createExplosion(x, y, count = 20, config = {}) {
        const defaultConfig = {
            minSpeed: 50,
            maxSpeed: 150,
            minSize: 2,
            maxSize: 6,
            life: 1,
            colors: ['#ff4444', '#ff8844', '#ffff44', '#ffffff'],
            gravity: 100,
            friction: 0.95,
            type: 'circle'
        };

        const finalConfig = { ...defaultConfig, ...config };

        for (let i = 0; i < count; i++) {
            const particle = this.pool.get();
            const angle = (Math.PI * 2 * i) / count + MathUtils.randomFloat(-0.5, 0.5);
            const speed = MathUtils.randomFloat(finalConfig.minSpeed, finalConfig.maxSpeed);

            particle.x = x;
            particle.y = y;
            particle.vx = Math.cos(angle) * speed;
            particle.vy = Math.sin(angle) * speed;
            particle.size = MathUtils.randomFloat(finalConfig.minSize, finalConfig.maxSize);
            particle.life = finalConfig.life;
            particle.maxLife = finalConfig.life;
            particle.color = ArrayUtils.randomChoice(finalConfig.colors);
            particle.gravity = finalConfig.gravity;
            particle.friction = finalConfig.friction;
            particle.type = finalConfig.type;
            particle.active = true;

            this.particles.push(particle);
        }
    }

    // 创建火花效果
    createSparks(x, y, count = 10, config = {}) {
        const defaultConfig = {
            minSpeed: 100,
            maxSpeed: 200,
            minSize: 1,
            maxSize: 3,
            life: 0.5,
            colors: ['#ffff44', '#ffffff', '#ff8844'],
            gravity: 200,
            friction: 0.98,
            type: 'spark'
        };

        const finalConfig = { ...defaultConfig, ...config };

        for (let i = 0; i < count; i++) {
            const particle = this.pool.get();
            const angle = MathUtils.randomFloat(0, Math.PI * 2);
            const speed = MathUtils.randomFloat(finalConfig.minSpeed, finalConfig.maxSpeed);

            particle.x = x;
            particle.y = y;
            particle.vx = Math.cos(angle) * speed;
            particle.vy = Math.sin(angle) * speed;
            particle.size = MathUtils.randomFloat(finalConfig.minSize, finalConfig.maxSize);
            particle.life = finalConfig.life;
            particle.maxLife = finalConfig.life;
            particle.color = ArrayUtils.randomChoice(finalConfig.colors);
            particle.gravity = finalConfig.gravity;
            particle.friction = finalConfig.friction;
            particle.type = finalConfig.type;
            particle.active = true;

            this.particles.push(particle);
        }
    }

    // 创建烟雾效果
    createSmoke(x, y, count = 15, config = {}) {
        const defaultConfig = {
            minSpeed: 20,
            maxSpeed: 50,
            minSize: 3,
            maxSize: 8,
            life: 2,
            colors: ['#666666', '#888888', '#aaaaaa'],
            gravity: -20,
            friction: 0.99,
            type: 'circle'
        };

        const finalConfig = { ...defaultConfig, ...config };

        for (let i = 0; i < count; i++) {
            const particle = this.pool.get();
            const angle = MathUtils.randomFloat(-Math.PI/4, -3*Math.PI/4);
            const speed = MathUtils.randomFloat(finalConfig.minSpeed, finalConfig.maxSpeed);

            particle.x = x + MathUtils.randomFloat(-10, 10);
            particle.y = y + MathUtils.randomFloat(-5, 5);
            particle.vx = Math.cos(angle) * speed;
            particle.vy = Math.sin(angle) * speed;
            particle.size = MathUtils.randomFloat(finalConfig.minSize, finalConfig.maxSize);
            particle.life = finalConfig.life;
            particle.maxLife = finalConfig.life;
            particle.color = ArrayUtils.randomChoice(finalConfig.colors);
            particle.gravity = finalConfig.gravity;
            particle.friction = finalConfig.friction;
            particle.type = finalConfig.type;
            particle.active = true;

            this.particles.push(particle);
        }
    }

    // 创建子弹击中效果
    createBulletHit(x, y, direction = 0, config = {}) {
        const defaultConfig = {
            count: 8,
            minSpeed: 80,
            maxSpeed: 120,
            minSize: 1,
            maxSize: 3,
            life: 0.3,
            colors: ['#ffff44', '#ffffff'],
            gravity: 150,
            friction: 0.96,
            type: 'spark'
        };

        const finalConfig = { ...defaultConfig, ...config };

        for (let i = 0; i < finalConfig.count; i++) {
            const particle = this.pool.get();
            const spread = Math.PI / 3; // 60度扩散
            const angle = direction + MathUtils.randomFloat(-spread/2, spread/2);
            const speed = MathUtils.randomFloat(finalConfig.minSpeed, finalConfig.maxSpeed);

            particle.x = x;
            particle.y = y;
            particle.vx = Math.cos(angle) * speed;
            particle.vy = Math.sin(angle) * speed;
            particle.size = MathUtils.randomFloat(finalConfig.minSize, finalConfig.maxSize);
            particle.life = finalConfig.life;
            particle.maxLife = finalConfig.life;
            particle.color = ArrayUtils.randomChoice(finalConfig.colors);
            particle.gravity = finalConfig.gravity;
            particle.friction = finalConfig.friction;
            particle.type = finalConfig.type;
            particle.active = true;

            this.particles.push(particle);
        }
    }

    // 创建血液效果
    createBlood(x, y, direction = 0, config = {}) {
        const defaultConfig = {
            count: 12,
            minSpeed: 60,
            maxSpeed: 100,
            minSize: 2,
            maxSize: 4,
            life: 1.5,
            colors: ['#cc0000', '#990000', '#660000'],
            gravity: 200,
            friction: 0.95,
            type: 'circle'
        };

        const finalConfig = { ...defaultConfig, ...config };

        for (let i = 0; i < finalConfig.count; i++) {
            const particle = this.pool.get();
            const spread = Math.PI / 2; // 90度扩散
            const angle = direction + MathUtils.randomFloat(-spread/2, spread/2);
            const speed = MathUtils.randomFloat(finalConfig.minSpeed, finalConfig.maxSpeed);

            particle.x = x;
            particle.y = y;
            particle.vx = Math.cos(angle) * speed;
            particle.vy = Math.sin(angle) * speed;
            particle.size = MathUtils.randomFloat(finalConfig.minSize, finalConfig.maxSize);
            particle.life = finalConfig.life;
            particle.maxLife = finalConfig.life;
            particle.color = ArrayUtils.randomChoice(finalConfig.colors);
            particle.gravity = finalConfig.gravity;
            particle.friction = finalConfig.friction;
            particle.type = finalConfig.type;
            particle.active = true;

            this.particles.push(particle);
        }
    }

    update(deltaTime) {
        // 更新所有粒子
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.update(deltaTime);

            // 移除非活跃粒子
            if (!particle.active) {
                this.pool.release(particle);
                this.particles.splice(i, 1);
            }
        }
    }

    render(ctx) {
        // 渲染所有粒子
        for (const particle of this.particles) {
            particle.render(ctx);
        }
    }

    clear() {
        // 清除所有粒子
        for (const particle of this.particles) {
            this.pool.release(particle);
        }
        this.particles.length = 0;
    }

    getParticleCount() {
        return this.particles.length;
    }
}

// 创建全局粒子系统实例
window.particleSystem = new ParticleSystem();
