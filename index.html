<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魂斗罗 - Web版</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="gameContainer">
        <div id="startMenu" class="menu">
            <h1>魂斗罗</h1>
            <div class="menu-options">
                <button id="startButton" class="menu-button">开始游戏</button>
                <button id="instructionsButton" class="menu-button">游戏说明</button>
            </div>
        </div>
        
        <div id="gameScreen" class="hidden">
            <canvas id="gameCanvas" width="800" height="600"></canvas>
            <div id="gameUI">
                <div id="playerStats">
                    <div id="lives">生命: <span id="livesCount">3</span></div>
                    <div id="score">得分: <span id="scoreValue">0</span></div>
                    <div id="weapon">武器: <span id="weaponType">普通</span></div>
                </div>
            </div>
        </div>
        
        <div id="pauseMenu" class="menu hidden">
            <h2>游戏暂停</h2>
            <div class="menu-options">
                <button id="resumeButton" class="menu-button">继续游戏</button>
                <button id="restartButton" class="menu-button">重新开始</button>
                <button id="mainMenuButton" class="menu-button">主菜单</button>
            </div>
        </div>
        
        <div id="gameOverMenu" class="menu hidden">
            <h2>游戏结束</h2>
            <div id="finalScore">最终得分: <span id="finalScoreValue">0</span></div>
            <div class="menu-options">
                <button id="playAgainButton" class="menu-button">再玩一次</button>
                <button id="backToMenuButton" class="menu-button">返回主菜单</button>
            </div>
        </div>
        
        <div id="instructions" class="menu hidden">
            <h2>游戏说明</h2>
            <div class="instructions-content">
                <p><strong>操作方式:</strong></p>
                <ul>
                    <li>WASD 或 方向键: 移动角色</li>
                    <li>空格键: 射击</li>
                    <li>P键: 暂停游戏</li>
                    <li>R键: 重新开始</li>
                </ul>
                <p><strong>游戏目标:</strong></p>
                <ul>
                    <li>消灭所有敌人</li>
                    <li>收集道具提升武器</li>
                    <li>尽可能获得高分</li>
                </ul>
            </div>
            <button id="backButton" class="menu-button">返回</button>
        </div>
    </div>
    
    <!-- 游戏脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/input.js"></script>
    <script src="js/collision.js"></script>
    <script src="js/particle.js"></script>
    <script src="js/bullet.js"></script>
    <script src="js/weapon.js"></script>
    <script src="js/player.js"></script>
    <script src="js/enemy.js"></script>
    <script src="js/powerup.js"></script>
    <script src="js/level.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
